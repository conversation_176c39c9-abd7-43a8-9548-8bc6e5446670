-- 为产品重复检查功能添加数据库索引（基于现有表结构）
-- 执行此脚本以优化查询性能

USE kmlc_plc;

-- ===== 检查并创建重复检查优化索引 =====

-- 1. 为product_inspection_status表创建复合索引
-- 优化查询: work_order_id + stage + inspector_role + submission_type
SET @sql = '';
SELECT COUNT(*) INTO @index_exists 
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
    AND table_name = 'product_inspection_status' 
    AND index_name = 'idx_duplicate_check';

SET @sql = CASE 
    WHEN @index_exists = 0 THEN 
        'CREATE INDEX idx_duplicate_check ON product_inspection_status (work_order_id, stage, inspector_role, submission_type);'
    ELSE 
        'SELECT "索引 idx_duplicate_check 已存在" as message;'
END;

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 2. 为products表创建work_order_id + serial_no复合索引
SET @sql = '';
SELECT COUNT(*) INTO @index_exists 
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
    AND table_name = 'products' 
    AND index_name = 'idx_products_work_order_serial';

SET @sql = CASE 
    WHEN @index_exists = 0 THEN 
        'CREATE INDEX idx_products_work_order_serial ON products (work_order_id, serial_no);'
    ELSE 
        'SELECT "索引 idx_products_work_order_serial 已存在" as message;'
END;

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ===== 验证索引创建结果 =====
SELECT 
    '产品重复检查功能索引创建完成' as message,
    NOW() as created_at;

-- 显示相关索引
SELECT 
    TABLE_NAME as '表名',
    INDEX_NAME as '索引名',
    GROUP_CONCAT(COLUMN_NAME ORDER BY SEQ_IN_INDEX) as '索引列'
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
    AND table_name IN ('product_inspection_status', 'products')
    AND index_name IN ('idx_duplicate_check', 'idx_products_work_order_serial')
GROUP BY TABLE_NAME, INDEX_NAME
ORDER BY TABLE_NAME;