-- ===== 产品重复检查功能 - 数据库索引优化脚本 =====
-- 文件: optimize_duplicate_check_indexes.sql
-- 用途: 为产品重复检查查询创建优化索引，提高查询性能
-- 基于表: product_inspection_status, products

USE kmlc_plc;

-- ===== 1. 重复检查核心查询优化索引 =====

-- 检查是否已存在重复检查优化索引
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '索引已存在'
        ELSE '索引不存在，需要创建'
    END as idx_duplicate_check_status
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
    AND table_name = 'product_inspection_status' 
    AND index_name = 'idx_duplicate_check';

-- 创建重复检查优化索引（如果不存在）
-- 此索引优化查询: work_order_id + stage + inspector_role + submission_type
CREATE INDEX IF NOT EXISTS idx_duplicate_check 
ON product_inspection_status (work_order_id, stage, inspector_role, submission_type);

-- ===== 2. Products表查询优化索引 =====

-- 检查products表的work_order_id + serial_no复合索引
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '索引已存在'
        ELSE '索引不存在，需要创建'
    END as idx_products_composite_status
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
    AND table_name = 'products' 
    AND index_name = 'idx_products_work_order_serial';

-- 创建products表复合索引（如果不存在）
-- 此索引优化联合查询: work_order_id + serial_no
CREATE INDEX IF NOT EXISTS idx_products_work_order_serial 
ON products (work_order_id, serial_no);

-- ===== 3. 额外的性能优化索引 =====

-- 为product_inspection_status表添加product_id索引（如果数据库中没有）
-- 这个索引优化联合查询中的JOIN操作
CREATE INDEX IF NOT EXISTS idx_pis_product_id_extended 
ON product_inspection_status (product_id, stage, inspector_role, submission_type);

-- ===== 4. 索引使用情况分析查询 =====

-- 查看所有相关表的索引情况
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    SEQ_IN_INDEX,
    CARDINALITY,
    INDEX_TYPE
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
    AND table_name IN ('product_inspection_status', 'products', 'quality_inspection_work_orders')
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- ===== 5. 重复检查查询性能测试 =====

-- 测试查询1: 检查特定产品的重复检验状态
-- 这个查询模拟前端API调用的实际查询
EXPLAIN SELECT 
    pis.id,
    pis.product_id,
    pis.inspector,
    pis.inspection_time,
    pis.submission_type,
    p.serial_no
FROM product_inspection_status pis
INNER JOIN products p ON pis.product_id = p.id
WHERE pis.work_order_id = 1 
    AND p.serial_no = 'TEST001' 
    AND pis.stage = 'assembly' 
    AND pis.inspector_role = 'first'
    AND pis.submission_type = 'final'
LIMIT 1;

-- 测试查询2: 统计工单中各阶段的检验完成情况
EXPLAIN SELECT 
    pis.stage,
    pis.inspector_role,
    COUNT(*) as completed_count
FROM product_inspection_status pis
WHERE pis.work_order_id = 1
    AND pis.submission_type = 'final'
GROUP BY pis.stage, pis.inspector_role;

-- ===== 6. 索引大小和使用统计 =====

-- 查看索引大小
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    ROUND(((INDEX_LENGTH) / 1024 / 1024), 2) AS 'Index Size (MB)'
FROM information_schema.TABLES 
WHERE table_schema = DATABASE()
    AND TABLE_NAME IN ('product_inspection_status', 'products')
    AND INDEX_LENGTH > 0
ORDER BY INDEX_LENGTH DESC;

-- ===== 7. 索引效果验证查询 =====

-- 验证重复检查查询的执行计划
-- 期望结果: 使用idx_duplicate_check和idx_products_work_order_serial索引
SELECT 
    '=== 重复检查查询执行计划 ===' as info;

-- 实际的重复检查查询（用于验证索引效果）
-- 注意: 请将参数替换为实际的测试数据
-- EXPLAIN FORMAT=JSON 
-- SELECT 
--     pis.id,
--     pis.product_id,
--     pis.inspector,
--     pis.inspection_time,
--     pis.submission_type,
--     p.serial_no
-- FROM product_inspection_status pis
-- INNER JOIN products p ON pis.product_id = p.id
-- WHERE pis.work_order_id = ? 
--     AND p.serial_no = ? 
--     AND pis.stage = ? 
--     AND pis.inspector_role = ?
--     AND pis.submission_type = 'final'
-- LIMIT 1;

-- ===== 8. 性能监控建议 =====

/*
性能监控建议:

1. 查询响应时间监控:
   - 重复检查API应在100ms内响应
   - 如果超过500ms，需要检查索引使用情况

2. 索引使用情况监控:
   - 定期检查EXPLAIN结果，确保使用了正确的索引
   - 监控慢查询日志

3. 数据增长监控:
   - 随着数据量增长，定期分析索引效果
   - 考虑是否需要分区或其他优化策略

4. 索引维护:
   - 定期使用ANALYZE TABLE更新索引统计信息
   - 监控索引碎片化情况
*/

-- ===== 9. 索引维护命令 =====

-- 分析表，更新索引统计信息（建议定期执行）
-- ANALYZE TABLE product_inspection_status;
-- ANALYZE TABLE products;
-- ANALYZE TABLE quality_inspection_work_orders;

-- 优化表，重建索引（在数据量大且有碎片时执行）
-- OPTIMIZE TABLE product_inspection_status;
-- OPTIMIZE TABLE products;

-- ===== 10. 验证脚本完成 =====

SELECT 
    '===== 索引优化脚本执行完成 =====' as message,
    NOW() as execution_time,
    DATABASE() as database_name;

-- 显示最终的索引状态
SELECT 
    '当前数据库中与重复检查相关的索引:' as info;

SELECT 
    TABLE_NAME as '表名',
    INDEX_NAME as '索引名',
    GROUP_CONCAT(COLUMN_NAME ORDER BY SEQ_IN_INDEX) as '索引列',
    INDEX_TYPE as '索引类型'
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
    AND table_name IN ('product_inspection_status', 'products')
    AND index_name IN (
        'idx_duplicate_check', 
        'idx_products_work_order_serial', 
        'idx_pis_product_id_extended',
        'idx_products_serial_no',  -- 原有索引
        'idx_pis_work_order',      -- 原有索引
        'idx_pis_product'          -- 原有索引
    )
GROUP BY TABLE_NAME, INDEX_NAME, INDEX_TYPE
ORDER BY TABLE_NAME, INDEX_NAME;