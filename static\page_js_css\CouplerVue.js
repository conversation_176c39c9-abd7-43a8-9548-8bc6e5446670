// 耦合器模块Vue版本 - 现代化UI重构版
(function() {
    'use strict';
    
    Logger.log('Loading Coupler Controller Vue App with Modern UI...');
    
    // 检查依赖
    if (!window.Vue || !window.ElementPlus) {
        Logger.error('Vue or ElementPlus not loaded');
        return;
    }

    const { createApp, ref, reactive, computed, onMounted, nextTick, watch } = Vue;
    const { ElMessage, ElMessageBox, ElLoading } = ElementPlus;
    
    const CouplerControllerVueApp = {
        setup() {
            // 响应式数据
            const loading = ref(false);
            const requiresPcbaCheck = ref(true);
            const basicInfoCollapsed = ref(false);
            const showTestLog = ref(true);
            const autoScroll = ref(true);
            const isDarkMode = ref(true); // 默认深色主题
            const showConfirmDialog = ref(false);
            const confirmAction = ref(null);
            const testLogs = ref([]);
            const currentTestIndex = ref(-1);
            const testRunning = ref(false);
            const autoSubmitEnabled = ref(false); // 自动提交开关，默认关闭
            
            // 表单数据
            const formData = reactive({
                // 基本信息
                tester: '',
                testTime: '',
                orderNumber: '',
                productionQuantity: '',
                productCode: '',
                productModel: '',
                productStatus: '',
                productSN: '',
                batchNumber: '',
                snByteCount: '',
                remarks: '',
                
                // 版本信息 - CouplerVue特有
                autoVersion: '',
                autoDate: '',
                couplerVersion: '',
                couplerBuildDate: ''
            });
            
            // 测试项目数据 - CouplerVue现代化UI版本
            const testItems = [
                { name: "Backplane Bus通信", code: "backplane_bus", result: "", category: "通信", icon: "database" },
                { name: "Body I/O输入输出", code: "body_io", result: "", category: "硬件", icon: "zap" },
                { name: "Led数码管", code: "led_tube", result: "", category: "硬件", icon: "zap" },
                { name: "Led灯珠", code: "led_bulb", result: "", category: "硬件", icon: "zap" },
                { name: "网口", code: "net_port", result: "", category: "接口", icon: "network" }
            ];
            
            const testResults = ref([...testItems]);
            const selectAll = ref(false);
            
            // 表单验证规则
            const rules = reactive({
                tester: [{ required: true, message: '请输入测试人员', trigger: 'blur' }],
                orderNumber: [{ required: true, message: '请输入加工单号', trigger: 'blur' }],
                productionQuantity: [{ required: true, message: '请输入生产数量', trigger: 'blur' }],
                productCode: [{ required: true, message: '请输入产品编码', trigger: 'blur' }],
                productModel: [{ required: true, message: '请输入产品型号', trigger: 'blur' }],
                productStatus: [{ required: true, message: '请选择产品状态', trigger: 'change' }],
                productSN: [{ required: true, message: '请输入产品SN号', trigger: 'blur' }],
                snByteCount: [{ required: true, message: '请输入产品SN号字节数', trigger: 'blur' }],
                couplerVersion: [{ required: true, message: '请输入耦合器软件版本', trigger: 'blur' }],
                couplerBuildDate: [{ required: true, message: '请输入耦合器构建日期', trigger: 'blur' }]
            });
            
            // 表单引用
            const formRef = ref(null);
            
            // 产品状态选项
            const productStatusOptions = [
                { label: '选择产品状态', value: '', disabled: true },
                { label: '新品', value: 'new' },
                { label: '维修', value: 'used' },
                { label: '返工', value: 'refurbished' }
            ];
            
            // ===== 性能优化：防抖函数提取为全局复用 =====
            
            // 防抖函数缓存 - 避免每次渲染重新创建
            const debouncedFunctions = new Map();
            
            const getDebounced = (key, func, wait) => {
                if (!debouncedFunctions.has(key)) {
                    let timeout;
                    const debouncedFunc = function executedFunction(...args) {
                        const later = () => {
                            clearTimeout(timeout);
                            func(...args);
                        };
                        clearTimeout(timeout);
                        timeout = setTimeout(later, wait);
                    };
                    debouncedFunctions.set(key, debouncedFunc);
                }
                return debouncedFunctions.get(key);
            };
            
            // 计算属性
            const passedTests = computed(() => 
                testResults.value.filter(item => item.result === 'pass').length
            );
            
            const failedTests = computed(() => 
                testResults.value.filter(item => item.result === 'fail').length
            );
            
            const totalTests = computed(() => testResults.value.length);
            
            const testProgress = computed(() => 
                ((passedTests.value + failedTests.value) / totalTests.value) * 100
            );
            
            const overallResult = computed(() => {
                if (failedTests.value > 0) {
                    return 'NG';
                } else if (passedTests.value === totalTests.value && totalTests.value > 0) {
                    return 'PASS';
                }
                return '';
            });
            
            // BEM架构重构：样式计算属性
            const toolbarClasses = computed(() => ({
                'glass-effect': true,
                'border-b': true,
                'py-4': true,
                'shadow-xl': true,
                'backdrop-blur-xl': true,
                'coupler-controller__toolbar': true,
                'coupler-controller__toolbar--dark': isDarkMode.value,
                'coupler-controller__toolbar--light': !isDarkMode.value
            }));
            
            const testSectionClasses = computed(() => ({
                'coupler-controller__test-section': true,
                'test-section': true
            }));
            
            const progressBarStyle = computed(() => ({
                '--progress-width': `${testProgress.value}%`
            }));
            
            // ===== 业务逻辑功能 =====
            
            // ===== 增强的焦点管理和错误提示系统 =====
            
            // 字段中文名称映射表
            const fieldNameMap = {
                tester: '测试人员',
                orderNumber: '加工单号',
                productionQuantity: '生产数量',
                productCode: '产品编码',
                productModel: '产品型号',
                productStatus: '产品状态',
                productSN: '产品SN号',
                snByteCount: 'SN号字节数',
                couplerVersion: '耦合器软件版本',
                couplerBuildDate: '耦合器构建日期'
            };
            
            // 统一的焦点管理函数 - 支持错误字段聚焦
            const focusToField = (fieldType) => {
                nextTick(() => {
                    let input;
                    switch (fieldType) {
                        case 'sn':
                        case 'productSN':
                            // 尝试多个选择器，支持展开和折叠状态下的SN输入框
                            const snSelectors = [
                                'input[placeholder*="产品SN号"]',
                                'input[placeholder*="请输入产品SN号"]',
                                '.coupler-controller__form-collapsed input[placeholder*="SN"]',
                                '.coupler-controller__form-expanded input[placeholder*="SN"]'
                            ];
                            for (const selector of snSelectors) {
                                input = document.querySelector(selector);
                                if (input && input.offsetParent !== null) break; // 确保元素可见
                            }
                            break;
                        case 'couplerVersion':
                            input = document.querySelector('input[placeholder*="耦合器软件版本"]');
                            break;
                        case 'couplerDate':
                        case 'couplerBuildDate':
                            input = document.querySelector('input[placeholder*="耦合器构建日期"]');
                            break;
                        case 'tester':
                            input = document.querySelector('input[placeholder*="测试人员"]') || 
                                   document.querySelector('.el-form-item:has(label:contains("测试人员")) input');
                            break;
                        case 'orderNumber':
                            input = document.querySelector('input[placeholder*="工单号"]') ||
                                   document.querySelector('input[placeholder*="加工单号"]');
                            break;
                        case 'productionQuantity':
                            input = document.querySelector('.el-form-item:has(label:contains("生产数量")) input');
                            break;
                        case 'productCode':
                            input = document.querySelector('.el-form-item:has(label:contains("产品编码")) input');
                            break;
                        case 'productModel':
                            input = document.querySelector('.el-form-item:has(label:contains("产品型号")) input');
                            break;
                        case 'productStatus':
                            input = document.querySelector('.el-form-item:has(label:contains("产品状态")) .el-select__wrapper');
                            break;
                        case 'snByteCount':
                            input = document.querySelector('.el-form-item:has(label:contains("SN字节数")) input');
                            break;
                        default:
                            // 通用选择器，根据prop名查找
                            input = document.querySelector(`[data-prop="${fieldType}"] input`) ||
                                   document.querySelector(`[name="${fieldType}"]`) ||
                                   document.querySelector(`.el-form-item:has(label:contains("${fieldNameMap[fieldType]}")) input`);
                    }
                    
                    if (input) {
                        input.focus();
                        input.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        const fieldName = fieldNameMap[fieldType] || fieldType;
                        addTestLog('info', 'FOCUS', `焦点已设置到${fieldName}输入框`);
                    } else {
                        console.warn(`未找到字段 ${fieldType} 的输入框`);
                    }
                });
            };
            
            // 增强的表单验证错误处理函数
            const handleValidationErrors = (errors) => {
                if (!errors || Object.keys(errors).length === 0) return;
                
                // 获取第一个错误字段
                const firstErrorField = Object.keys(errors)[0];
                const firstError = errors[firstErrorField][0];
                const fieldName = fieldNameMap[firstErrorField] || firstErrorField;
                
                // 显示具体错误信息
                const errorMessage = `${fieldName}：${firstError.message}`;
                ElMessage({
                    message: errorMessage,
                    type: 'warning',
                    duration: 4000,
                    showClose: true
                });
                
                // 记录到测试日志
                addTestLog('warning', 'VALIDATE', `表单验证失败 - ${errorMessage}`);
                
                // 如果有多个错误，在日志中列出所有错误
                const allErrors = Object.keys(errors).map(field => {
                    const error = errors[field][0];
                    const name = fieldNameMap[field] || field;
                    return `${name}: ${error.message}`;
                });
                
                if (allErrors.length > 1) {
                    addTestLog('info', 'VALIDATE', `共发现 ${allErrors.length} 个验证错误：${allErrors.join('; ')}`);
                }
                
                // 自动聚焦到第一个错误字段
                focusToField(firstErrorField);
                
                return {
                    field: firstErrorField,
                    fieldName: fieldName,
                    message: firstError.message,
                    allErrors: allErrors
                };
            };
            
            // 版本一致性检查计算属性
            const isVersionConsistent = computed(() => {
                // 如果没有自动获取的版本信息，认为是一致的（没有对比基准）
                if (!formData.autoVersion && !formData.autoDate) return true;
                
                // 如果有自动获取的版本信息，检查是否与手动输入的一致
                let versionMatch = true;
                let dateMatch = true;
                
                if (formData.autoVersion) {
                    versionMatch = formData.couplerVersion && formData.autoVersion === formData.couplerVersion;
                }
                
                if (formData.autoDate) {
                    dateMatch = formData.couplerBuildDate && formData.autoDate === formData.couplerBuildDate;
                }
                
                return versionMatch && dateMatch;
            });
            
            // 工单号查询
            const queryOrderInfo = async (orderNumber) => {
                if (!orderNumber) {
                    // 清空相关字段
                    formData.productionQuantity = '';
                    formData.productCode = '';
                    formData.productModel = '';
                    formData.batchNumber = '';
                    formData.snByteCount = '';
                    requiresPcbaCheck.value = true;
                    addTestLog('info', 'ORDER', '工单号已清空，相关字段已重置');
                    return;
                }

                addTestLog('info', 'ORDER', `开始查询工单信息: ${orderNumber}`);
                try {
                    const response = await fetch(`/api/work-order/by-number?orderNumber=${encodeURIComponent(orderNumber)}`);
                    const data = await response.json();
                    
                    if (data.success && data.order) {
                        // 检查测试前阶段是否完成
                        if (!data.order.test_stage_completed) {
                            formData.productionQuantity = '';
                            formData.productCode = '';
                            formData.productModel = '';
                            formData.batchNumber = '';
                            formData.snByteCount = '';
                            addTestLog('warning', 'ORDER', '该工单未完成测试前阶段外观检验');
                            ElMessage.warning('该工单未完成测试前阶段外观检验');
                            requiresPcbaCheck.value = true;
                            return;
                        }
                        
                        // 自动填充字段
                        formData.productionQuantity = data.order.ord_productionQuantity;
                        formData.productCode = data.order.ord_productCode;
                        formData.productModel = data.order.ord_productModel;
                        formData.batchNumber = data.order.ord_probatch;
                        formData.snByteCount = data.order.ord_snlenth;
                        
                        // 设置PCBA检查标志
                        requiresPcbaCheck.value = data.order.ord_requires_pcba_check !== false;
                        
                        addTestLog('success', 'ORDER', `工单信息获取成功: ${formData.productModel} (${requiresPcbaCheck.value ? '需要PCBA绑定' : '无需PCBA绑定'})`);
                        Logger.log(`工单 ${orderNumber}: ${requiresPcbaCheck.value ? '需要PCBA绑定' : '无需PCBA绑定'}`);
                        
                        // 查询成功后自动折叠基本信息（仅在首次查询成功时）
                        if (!basicInfoCollapsed.value) {
                            basicInfoCollapsed.value = true;
                        }
                    } else {
                        // 清空相关字段
                        formData.productionQuantity = '';
                        formData.productCode = '';
                        formData.productModel = '';
                        formData.batchNumber = '';
                        formData.snByteCount = '';
                        
                        addTestLog('warning', 'ORDER', data.message || '未找到工单信息');
                        if (data.message) {
                            ElMessage.warning(data.message);
                        }
                        requiresPcbaCheck.value = true;
                    }
                } catch (error) {
                    Logger.error('Error fetching order details:', error);
                    addTestLog('error', 'ORDER', `获取工单信息失败: ${error.message}`);
                    ElMessage.error('获取工单信息失败');
                    requiresPcbaCheck.value = true;
                }
            };
            
            // 防抖查询工单信息 - 性能优化
            const debouncedQueryOrderInfo = getDebounced('queryOrderInfo', queryOrderInfo, 500);
            
            // ===== 输入处理函数 =====
            
            // 处理加工单号输入 - 大写转换和去除空格
            const handleOrderNumberInput = (value) => {
                if (typeof value === 'string') {
                    formData.orderNumber = value.trim().toUpperCase();
                }
            };
            
            // 处理产品SN号输入 - 大写转换和去除空格
            const handleProductSNInput = (value) => {
                if (typeof value === 'string') {
                    formData.productSN = value.trim().toUpperCase();
                }
            };
            
            // 自动获取版本信息
            const autoFetchVersionInfo = async (productSN) => {
                const productStatus = formData.productStatus;
                
                if (!['new', 'used', 'refurbished'].includes(productStatus)) {
                    addTestLog('info', 'VERSION', '产品状态不支持自动获取版本信息，跳过');
                    Logger.log('产品状态不支持自动获取版本信息，跳过');
                    return;
                }

                addTestLog('info', 'VERSION', `开始获取版本信息: SN=${productSN}, 状态=${productStatus}`);
                try {
                    const response = await fetch(`/api/coupler-controller-vue/get-version-info?product_sn=${encodeURIComponent(productSN)}&product_status=${encodeURIComponent(productStatus)}`);
                    const data = await response.json();
                    
                    if (data.success && data.data) {
                        if (data.data.software_version) {
                            formData.autoVersion = data.data.software_version;
                        }
                        
                        if (data.data.build_time) {
                            formData.autoDate = data.data.build_time;
                        }
                        
                        if (data.data.software_version || data.data.build_time) {
                            addTestLog('success', 'VERSION', `版本信息自动获取成功 (${productStatus}模式): 版本=${data.data.software_version || '无'}, 日期=${data.data.build_time || '无'}`);
                            Logger.log(`耦合器版本信息自动获取成功 (${productStatus}模式):`, data.message);
                        } else {
                            addTestLog('warning', 'VERSION', `版本信息为空 (${productStatus}模式): ${data.message}`);
                            Logger.log(`耦合器版本信息为空 (${productStatus}模式):`, data.message);
                        }
                    } else {
                        addTestLog('warning', 'VERSION', `未找到相关版本信息: ${data.message}`);
                        Logger.log('未找到相关版本信息或查询失败:', data.message);
                    }
                } catch (error) {
                    Logger.error('自动获取版本信息失败:', error);
                    addTestLog('error', 'VERSION', `自动获取版本信息失败: ${error.message}`);
                }
                
                // 版本信息获取完成后，触发自动提交检查
                if (autoSubmitEnabled.value) {
                    // 等待一小段时间确保版本信息已更新到UI
                    setTimeout(() => {
                        triggerAutoSubmitIfEnabled(productSN);
                    }, 500);
                }
            };
            
            // 新增：添加一个辅助函数，用于验证SN的订货号是否正确
            const validateSnOrderNumber = async (sn, productCode) => {
                if (!productCode) {
                    ElMessage.error('产品编码缺失，无法验证SN');
                    addTestLog('error', 'SN_CHECK', 'SN验证失败: 产品编码缺失');
                    return false;
                }
                try {
                    const response = await fetch(`/api/products/validate-sn-order?sn=${encodeURIComponent(sn)}&productCode=${encodeURIComponent(productCode)}`);
                    const result = await response.json();
                    
                    if (!result.success) {
                        ElMessage.error(result.message || 'SN与工单订货号不匹配！');
                        addTestLog('error', 'SN_CHECK', `SN验证失败: ${result.message || 'SN与工单订货号不匹配！'}`);
                        return false;
                    }
                    addTestLog('success', 'SN_CHECK', `SN订货号验证通过: ${sn}`);
                    return true;
                } catch (error) {
                    Logger.error('验证 SN 订货号失败:', error);
                    ElMessage.error('验证SN失败，请稍后重试');
                    addTestLog('error', 'SN_CHECK', `SN验证异常: ${error.message}`);
                    return false;
                }
            };
            
            // SN检查
            const checkSN = async (sn) => {
                if (!sn) return;
                
                addTestLog('info', 'SN_CHECK', `开始检查SN号: ${sn}`);
                
                // 新增步骤1：验证SN的订货号是否正确
                const isOrderNumberValid = await validateSnOrderNumber(sn, formData.productCode);
                if (!isOrderNumberValid) {
                    formData.productSN = ''; // 清空输入框
                    focusToField('sn');    // 重新获取焦点
                    return; // 如果订货号不匹配，则中断后续所有操作
                }
                
                // 如果当前工单不需要PCBA检查，则跳过检查
                if (!requiresPcbaCheck.value) {
                    addTestLog('info', 'SN_CHECK', '当前工单无需PCBA绑定检查，允许继续测试');
                    Logger.log('当前工单无需PCBA绑定检查，允许继续测试');
                    await autoFetchVersionInfo(sn);
                    return;
                }

                try {
                    const response = await fetch(`/api/coupler-controller-vue/check-sn?sn=${encodeURIComponent(sn)}`);
                    const data = await response.json();
                    
                    if (data.success) {
                        if (!data.exists) {
                            addTestLog('error', 'SN_CHECK', `SN号未绑定PCBA: ${sn}`);
                            ElMessage.warning('该SN号未绑定PCBA！');
                            formData.productSN = '';
                            // 验证失败时聚焦到SN输入框
                            focusToField('sn');
                            return;
                        } else {
                            addTestLog('success', 'SN_CHECK', `SN号验证通过: ${sn}`);
                            await autoFetchVersionInfo(sn);
                        }
                    } else {
                        addTestLog('error', 'SN_CHECK', `检查SN号失败: ${data.message}`);
                        Logger.error('检查SN号失败:', data.message);
                        // API调用失败时也聚焦到SN输入框
                        focusToField('sn');
                    }
                } catch (error) {
                    Logger.error('检查SN号时发生错误:', error);
                    addTestLog('error', 'SN_CHECK', `检查SN号时发生错误: ${error.message}`);
                    ElMessage.error('检查SN号失败，请检查网络连接');
                    // 网络错误时也聚焦到SN输入框
                    focusToField('sn');
                }
            };
            
            // ===== 性能优化：企业级日志管理系统 - 对标IOModuleVue =====
            
            // 日志管理配置
            const logConfig = {
                maxLogs: 500,           // 最大日志条数
                batchSize: 50,          // 批量清理数量
                levels: ['system', 'success', 'error', 'warning', 'info'],
                enabledLevels: ref(['system', 'success', 'error', 'warning', 'info'])
            };
            
            // 性能优化的日志添加函数
            const addTestLog = (level, category, message, details) => {
                // 性能优化：日志条数限制
                if (testLogs.value.length >= logConfig.maxLogs) {
                    testLogs.value = testLogs.value.slice(-logConfig.maxLogs + logConfig.batchSize);
                }
                
                // 级别过滤：只添加启用的日志级别
                if (!logConfig.enabledLevels.value.includes(level)) {
                    return;
                }
                
                const newLog = {
                    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
                    timestamp: new Date().toLocaleTimeString('zh-CN', {
                        hour12: false,
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        fractionalSecondDigits: 3,
                    }),
                    level,
                    category,
                    message,
                    details,
                };
                testLogs.value.push(newLog);

                // 性能优化：防抖滚动
                if (autoScroll.value) {
                    const scrollToBottom = getDebounced('logScroll', () => {
                        const logContainer = document.getElementById('coupler-test-log-container');
                        if (logContainer) {
                            logContainer.scrollTop = logContainer.scrollHeight;
                        }
                    }, 100);
                    nextTick(scrollToBottom);
                }
            };
            
            // 日志级别过滤
            const filteredLogs = computed(() => {
                return testLogs.value.filter(log => 
                    logConfig.enabledLevels.value.includes(log.level)
                );
            });
            
            // 日志导出功能
            const exportLogs = () => {
                const logText = filteredLogs.value.map(log => 
                    `[${log.timestamp}] [${log.category}] ${log.level.toUpperCase()}: ${log.message}${log.details ? ' - ' + log.details : ''}`
                ).join('\n');
                
                const blob = new Blob([logText], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `coupler-test-log-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                ElMessage.success('测试日志已导出');
                addTestLog('info', 'EXPORT', `导出 ${filteredLogs.value.length} 条日志记录`);
            };
            
            // 日志级别切换
            const toggleLogLevel = (level) => {
                const index = logConfig.enabledLevels.value.indexOf(level);
                if (index > -1) {
                    logConfig.enabledLevels.value.splice(index, 1);
                } else {
                    logConfig.enabledLevels.value.push(level);
                }
            };

            // 性能优化的自动测试功能
            const runAutoTest = async () => {
                testRunning.value = true;
                currentTestIndex.value = 0;
                showTestLog.value = true;

                addTestLog('system', 'SYSTEM', '=== 耦合器自动测试开始 ===');
                addTestLog('info', 'INIT', '初始化测试环境...');
                addTestLog('info', 'VERSION', `检查版本信息: ${formData.couplerVersion || '未设置'}`);
                addTestLog('success', 'VERSION', '版本检查完成，开始执行测试序列');

                try {
                    for (let i = 0; i < testResults.value.length; i++) {
                        // 检查是否需要停止测试
                        if (!testRunning.value) {
                            addTestLog('warning', 'SYSTEM', '测试被手动停止');
                            return;
                        }

                        currentTestIndex.value = i;
                        const currentTest = testResults.value[i];

                        addTestLog('info', 'TEST', `开始测试: ${currentTest.name}`, `类别: ${currentTest.category}`);
                        testResults.value[i].result = 'testing';

                        // 性能优化：使用performance.now()计时
                        const testStartTime = performance.now();
                        
                        // 模拟测试过程 - 配置化失败率
                        await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

                        const result = Math.random() > 0.15 ? 'pass' : 'fail';
                        const duration = Math.round(performance.now() - testStartTime);

                        testResults.value[i].result = result;
                        testResults.value[i].duration = duration;

                        if (result === 'pass') {
                            addTestLog('success', 'RESULT', `✓ ${currentTest.name} 测试通过`, `耗时: ${duration}ms`);
                        } else {
                            addTestLog('error', 'RESULT', `✗ ${currentTest.name} 测试失败`, `耗时: ${duration}ms`);
                        }
                    }

                    currentTestIndex.value = -1;
                    testRunning.value = false;

                    addTestLog('system', 'SUMMARY', '=== 测试完成 ===');
                    addTestLog('info', 'SUMMARY', `总计: ${totalTests.value} 项, 通过: ${passedTests.value} 项, 失败: ${failedTests.value} 项`);

                    ElMessage.success(`耦合器测试完成，通过 ${passedTests.value}/${totalTests.value} 项`);
                    
                } catch (error) {
                    addTestLog('error', 'SYSTEM', `测试异常: ${error.message}`);
                    ElMessage.error('测试过程中发生错误');
                } finally {
                    // 性能优化：批量重新渲染图标，避免重复调用
                    const refreshIcons = getDebounced('iconRefresh', () => {
                        if (window.lucide) {
                            window.lucide.createIcons();
                        }
                    }, 200);
                    
                    nextTick(refreshIcons);
                }
            };

            // 停止测试
            const stopTest = () => {
                testRunning.value = false;
                currentTestIndex.value = -1;
                // 重置正在测试的项目状态为空
                testResults.value.forEach(item => {
                    if (item.result === 'testing') {
                        item.result = '';
                    }
                });
                addTestLog('warning', 'SYSTEM', '=== 测试已手动停止 ===');
                ElMessage.info('测试已停止');
            };

            // 日志级别样式
            const getLogLevelClass = (level) => {
                const classes = {
                    success: 'log-success',
                    error: 'log-error',
                    warning: 'log-warning',
                    info: 'log-info',
                    system: 'log-system'
                };
                return classes[level] || 'log-info';
            };

            const getLogMessageClass = (level) => {
                const classes = {
                    success: 'text-green-300',
                    error: 'text-red-300',
                    warning: 'text-yellow-300',
                    info: 'text-gray-300',
                    system: 'text-purple-300'
                };
                return classes[level] || 'text-gray-300';
            };
            
            // 清除测试日志
            const clearTestLogs = () => {
                testLogs.value = [];
                addTestLog('info', 'SYSTEM', '测试日志已清空');
            };

            // ===== 测试项目操作功能 =====

            // 设置测试结果 - 性能优化
            const setTestResult = (index, result) => {
                testResults.value[index].result = result;
                if (testResults.value[index].duration) {
                    delete testResults.value[index].duration;
                }
                
                // 性能优化：使用防抖的图标刷新
                const refreshIcons = getDebounced('manualIconRefresh', () => {
                    if (window.lucide) {
                        window.lucide.createIcons();
                    }
                }, 100);
                
                nextTick(refreshIcons);
                
                const testName = testResults.value[index].name;
                const statusText = result === 'pass' ? '通过' : '失败';
                ElMessage.success(`${testName} 已标记为${statusText}`);
                addTestLog('info', 'MANUAL', `手动设置测试结果: ${testName} = ${statusText}`);
            };

            // 清除所有测试结果 - 性能优化
            const clearAllResults = () => {
                testResults.value.forEach(item => {
                    item.result = '';
                    if (item.duration) {
                        delete item.duration;
                    }
                });
                
                // 性能优化：使用防抖的图标刷新
                const refreshIcons = getDebounced('clearAllIconRefresh', () => {
                    if (window.lucide) {
                        window.lucide.createIcons();
                    }
                }, 100);
                
                nextTick(refreshIcons);
                ElMessage.success('已清除所有测试结果');
                addTestLog('info', 'MANUAL', '手动清除所有测试结果');
            };

            // 设置所有测试为通过 - 性能优化
            const setAllPass = (showNotification = true) => {
                testResults.value.forEach(item => {
                    item.result = 'pass';
                });
                
                // 性能优化：使用防抖的图标刷新
                const refreshIcons = getDebounced('setAllPassIconRefresh', () => {
                    if (window.lucide) {
                        window.lucide.createIcons();
                    }
                }, 100);
                
                nextTick(refreshIcons);
                if (showNotification) {
                    ElMessage.success('已设置所有测试项为通过');
                    addTestLog('info', 'MANUAL', '手动设置所有测试项为通过');
                }
            };

            // 测试项目图标样式计算
            const getTestItemIconClass = (result) => {
                const classes = {
                    pass: 'bg-green-100 text-green-600',
                    fail: 'bg-red-100 text-red-600',
                    testing: 'bg-blue-100 text-blue-600',
                    '': 'bg-gray-100 text-gray-500'
                };
                return classes[result] || 'bg-gray-100 text-gray-500';
            };

            // 基础功能函数
            const toggleBasicInfo = () => {
                basicInfoCollapsed.value = !basicInfoCollapsed.value;
                
                // 性能优化：使用防抖的图标刷新
                const refreshIcons = getDebounced('toggleBasicInfoRefresh', () => {
                    if (window.lucide) {
                        window.lucide.createIcons();
                    }
                }, 100);
                
                nextTick(refreshIcons);
            };
            
            const toggleTheme = () => {
                isDarkMode.value = !isDarkMode.value;
                applyTheme();
                saveThemePreference();
                ElMessage.success(`已切换到${isDarkMode.value ? '深色' : '浅色'}主题`);
            };

            const applyTheme = () => {
                const htmlElement = document.documentElement;
                
                if (isDarkMode.value) {
                    htmlElement.setAttribute('data-theme', 'dark');
                } else {
                    htmlElement.removeAttribute('data-theme');
                }
                
                // 性能优化：使用防抖的图标刷新
                const refreshIcons = getDebounced('applyThemeRefresh', () => {
                    if (window.lucide) {
                        window.lucide.createIcons();
                    }
                }, 100);
                
                nextTick(refreshIcons);
            };

            const saveThemePreference = () => {
                localStorage.setItem('coupler-theme-preference', isDarkMode.value ? 'dark' : 'light');
            };

            const loadThemePreference = () => {
                const saved = localStorage.getItem('coupler-theme-preference');
                if (saved) {
                    isDarkMode.value = saved === 'dark';
                } else {
                    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                        isDarkMode.value = true;
                    } else {
                        isDarkMode.value = false;
                    }
                }
            };
            
            // 提交表单
            const submitForm = async () => {
                addTestLog('info', 'SUBMIT', '开始提交耦合器测试数据...');
                Logger.log('开始提交耦合器测试数据...');
                
                try {
                    if (!formRef.value) {
                        Logger.error('表单引用不存在');
                        addTestLog('error', 'SUBMIT', '表单引用不存在，初始化失败');
                        ElMessage.error('表单初始化失败，请刷新页面重试');
                        return;
                    }
                    
                    // 增强的表单验证 - 捕获具体错误信息
                    let validationResult = null;
                    try {
                        await formRef.value.validate();
                        addTestLog('success', 'VALIDATE', '表单验证通过');
                    } catch (validationErrors) {
                        // 处理验证错误，显示具体错误信息
                        if (validationErrors && typeof validationErrors === 'object') {
                            const errorInfo = handleValidationErrors(validationErrors);
                            if (errorInfo) {
                                Logger.log('表单验证失败:', errorInfo);
                                return;
                            }
                        } else {
                            // 兜底处理：如果无法解析具体错误
                            Logger.log('表单验证失败（未知错误）');
                            addTestLog('warning', 'VALIDATE', '表单验证失败，请检查必填字段');
                            ElMessage.warning('请完善必填信息');
                            return;
                        }
                    }
                } catch (error) {
                    Logger.error('表单验证异常:', error);
                    addTestLog('error', 'VALIDATE', `表单验证异常: ${error.message}`);
                    ElMessage.error('表单验证出现异常，请刷新页面重试');
                    return;
                }
                
                // 验证版本信息一致性 - 增强错误提示
                if (!isVersionConsistent.value) {
                    if (formData.autoVersion && formData.couplerVersion && formData.autoVersion !== formData.couplerVersion) {
                        const errorMsg = `耦合器软件版本不匹配`;
                        const detailMsg = `自动获取版本"${formData.autoVersion}"与手动输入版本"${formData.couplerVersion}"不一致，请核实后重新输入`;
                        
                        addTestLog('error', 'VALIDATE', `软件版本不一致: 自动获取=${formData.autoVersion}, 手动输入=${formData.couplerVersion}`);
                        
                        ElMessage({
                            message: `${errorMsg}：${detailMsg}`,
                            type: 'error',
                            duration: 6000,
                            showClose: true
                        });
                        
                        // 版本不一致时聚焦到软件版本输入框
                        focusToField('couplerVersion');
                        return;
                    }
                    
                    if (formData.autoDate && formData.couplerBuildDate && formData.autoDate !== formData.couplerBuildDate) {
                        const errorMsg = `耦合器构建日期不匹配`;
                        const detailMsg = `自动获取日期"${formData.autoDate}"与手动输入日期"${formData.couplerBuildDate}"不一致，请核实后重新输入`;
                        
                        addTestLog('error', 'VALIDATE', `构建日期不一致: 自动获取=${formData.autoDate}, 手动输入=${formData.couplerBuildDate}`);
                        
                        ElMessage({
                            message: `${errorMsg}：${detailMsg}`,
                            type: 'error',
                            duration: 6000,
                            showClose: true
                        });
                        
                        // 日期不一致时聚焦到构建日期输入框
                        focusToField('couplerBuildDate');
                        return;
                    }
                }
                addTestLog('success', 'VALIDATE', '版本信息一致性检查通过');
                
                // 验证产品SN号长度 - 增强错误提示
                const snByteCount = parseInt(formData.snByteCount);
                if (isNaN(snByteCount)) {
                    const errorMsg = 'SN号字节数格式错误';
                    const detailMsg = '请输入有效的数字作为SN号字节数';
                    
                    addTestLog('error', 'VALIDATE', '产品SN号字节数无效');
                    
                    ElMessage({
                        message: `${errorMsg}：${detailMsg}`,
                        type: 'error',
                        duration: 4000,
                        showClose: true
                    });
                    
                    focusToField('snByteCount');
                    return;
                }
                
                const productSN = String(formData.productSN || '');
                if (productSN.length !== snByteCount) {
                    const errorMsg = 'SN号长度不符合要求';
                    const detailMsg = `当前SN号"${productSN}"长度为${productSN.length}个字符，但要求长度为${snByteCount}个字符，请检查并重新输入`;
                    
                    addTestLog('error', 'VALIDATE', `产品SN号长度错误: 期望${snByteCount}字节, 实际${productSN.length}字节`);
                    
                    ElMessage({
                        message: `${errorMsg}：${detailMsg}`,
                        type: 'error',
                        duration: 6000,
                        showClose: true
                    });
                    
                    // SN号长度验证失败时聚焦到SN输入框
                    focusToField('productSN');
                    return;
                }
                addTestLog('success', 'VALIDATE', `产品SN号长度验证通过: ${productSN.length}字节`);
                
                // 收集表单数据
                const submitData = {
                    // 基本信息
                    tester: formData.tester,
                    test_time: formData.testTime,
                    work_order: String(formData.orderNumber || '').trim(),
                    work_qty: String(formData.productionQuantity || '').trim(),
                    pro_model: String(formData.productModel || '').trim(),
                    pro_code: String(formData.productCode || '').trim(),
                    pro_sn: String(formData.productSN || '').trim(),
                    pro_batch: formData.batchNumber || 'N/A',
                    remarks: formData.remarks || 'N/A',
                    
                    // 版本信息
                    couplersw_version: formData.couplerVersion || 'N/A',
                    coupler_date: formData.couplerBuildDate || 'N/A',
                    
                    // 测试结果
                    backplane_result: testResults.value[0].result,
                    body_io_result: testResults.value[1].result,
                    led_tube_result: testResults.value[2].result,
                    led_bulb_result: testResults.value[3].result,
                    net_port_result: testResults.value[4].result
                };
                
                // 产品状态映射
                const productStatusMap = {
                    'new': 1,
                    'used': 2,
                    'refurbished': 3
                };
                
                submitData.pro_status = productStatusMap[formData.productStatus];
                if (!submitData.pro_status) {
                    addTestLog('error', 'VALIDATE', '产品状态无效');
                    ElMessage.warning('请选择产品状态！');
                    return;
                }
                
                // 设置维修和返工次数
                submitData.maintenance = submitData.pro_status === 2 ? 1 : 0;
                submitData.rework = submitData.pro_status === 3 ? 1 : 0;
                
                // 转换测试结果为数字格式 (采用默认通过策略：只有明确标记为fail的才是失败)
                const testResultsConverted = {
                    backplane: submitData.backplane_result === 'fail' ? 2 : 1,
                    body_io: submitData.body_io_result === 'fail' ? 2 : 1,
                    led_tube: submitData.led_tube_result === 'fail' ? 2 : 1,
                    led_bulb: submitData.led_bulb_result === 'fail' ? 2 : 1,
                    net_port: submitData.net_port_result === 'fail' ? 2 : 1
                };
                
                Object.assign(submitData, testResultsConverted);
                
                // 确定整体测试状态
                const allTestsPassed = Object.values(testResultsConverted).every(result => result === 1);
                submitData.test_status = allTestsPassed ? 'pass' : 'ng';
                
                try {
                    loading.value = true;
                    addTestLog('info', 'SUBMIT', '正在向服务器提交数据...');
                    
                    const response = await fetch('/api/coupler-controller-vue/submit-test', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(submitData)
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        addTestLog('success', 'SUBMIT', '耦合器测试数据提交成功');
                        ElMessage.success('测试信息提交成功！');
                        
                        // 保存需要保留的字段值
                        const savedValues = {
                            tester: formData.tester,
                            orderNumber: formData.orderNumber,
                            productionQuantity: formData.productionQuantity,
                            productCode: formData.productCode,
                            productModel: formData.productModel,
                            productStatus: formData.productStatus,
                            batchNumber: formData.batchNumber,
                            snByteCount: formData.snByteCount,
                            remarks: formData.remarks,
                            couplerVersion: formData.couplerVersion,
                            couplerBuildDate: formData.couplerBuildDate
                        };
                        
                        // 重置表单
                        await formRef.value.resetFields();
                        
                        // 恢复保存的值
                        Object.assign(formData, savedValues);
                        
                        // 重置测试项目
                        testResults.value.forEach(item => {
                            item.result = '';
                            item.selected = false;
                        });
                        selectAll.value = false;
                        
                        // 清空特定字段
                        formData.productSN = '';
                        formData.autoVersion = '';
                        formData.autoDate = '';
                        
                        addTestLog('info', 'SUBMIT', '表单已重置，准备下一次测试');
                        
                        // 重新渲染图标确保状态正确显示
                        nextTick(() => {
                            if (window.lucide) {
                                window.lucide.createIcons();
                            }
                        });
                        
                        // 提交成功后聚焦到产品SN输入框（基本信息卡片状态保持不变）
                        focusToField('sn');
                        
                    } else {
                        addTestLog('error', 'SUBMIT', `提交失败: ${result.message || '未知错误'}`);
                        ElMessage.error(result.message || '提交失败，请重试');
                    }
                } catch (error) {
                    Logger.error('提交错误：', error);
                    addTestLog('error', 'SUBMIT', `网络错误: ${error.message || '请检查网络连接'}`);
                    ElMessage.error(`网络错误：${error.message || '请检查网络连接'}`);
                } finally {
                    loading.value = false;
                }
            };
            
            // ===== 自动提交功能 =====
            
            // 执行自动校验
            const performAutoValidation = () => {
                const validationResults = {
                    snValid: false,
                    versionConsistent: false,
                    formValid: false,
                    errors: []
                };
                
                // 1. SN有效性校验
                const productSN = String(formData.productSN || '');
                const snByteCount = parseInt(formData.snByteCount);
                
                if (!productSN) {
                    validationResults.errors.push('产品SN号为空');
                } else if (isNaN(snByteCount)) {
                    validationResults.errors.push('SN号字节数无效');
                } else if (productSN.length !== snByteCount) {
                    validationResults.errors.push(`SN号长度不符合要求: 期望${snByteCount}字节, 实际${productSN.length}字节`);
                } else {
                    validationResults.snValid = true;
                }
                
                // 2. 版本比对校验
                if (isVersionConsistent.value) {
                    validationResults.versionConsistent = true;
                } else {
                    validationResults.errors.push('版本信息不一致');
                }
                
                // 3. 表单基本校验
                if (formData.tester && formData.orderNumber && formData.productCode && 
                    formData.productModel && formData.couplerVersion && formData.couplerBuildDate) {
                    validationResults.formValid = true;
                } else {
                    validationResults.errors.push('必填字段未完整填写');
                }
                
                validationResults.allValid = validationResults.snValid && 
                                          validationResults.versionConsistent && 
                                          validationResults.formValid;
                
                return validationResults;
            };
            
            // 触发自动提交流程
            const triggerAutoSubmitIfEnabled = async (sn) => {
                if (!autoSubmitEnabled.value) {
                    return;
                }
                
                addTestLog('info', 'AUTO_SUBMIT', `开始自动提交校验流程: SN=${sn}`);
                
                // 执行自动校验
                const validation = performAutoValidation();
                
                if (!validation.allValid) {
                    const firstError = validation.errors[0] || '未知校验错误';
                    addTestLog('warning', 'AUTO_SUBMIT', `自动提交校验失败，转为手动模式: ${validation.errors.join('; ')}`);
                    ElMessage({
                        message: `自动提交失败：${firstError}。请手动检查并提交。`,
                        type: 'warning',
                        duration: 5000,
                        showClose: true,
                    });
                    return;
                }
                
                addTestLog('success', 'AUTO_SUBMIT', '所有校验通过，开始自动提交流程');
                
                try {
                    // 步骤A: 自动执行"全通过"操作
                    addTestLog('info', 'AUTO_SUBMIT', '执行自动全通过操作');
                    setAllPass(false);
                    
                    // 等待一小段时间确保UI更新
                    await new Promise(resolve => setTimeout(resolve, 800));
                    
                    // 步骤B: 自动执行"提交测试"操作
                    addTestLog('info', 'AUTO_SUBMIT', '执行自动提交测试操作');
                    await submitForm();
                    
                    addTestLog('success', 'AUTO_SUBMIT', '自动提交流程完成');
                    
                } catch (error) {
                    addTestLog('error', 'AUTO_SUBMIT', `自动提交过程中发生错误: ${error.message}`);
                    ElMessage.error('自动提交失败，请手动操作');
                }
            };
            
            // 生命周期
            onMounted(async () => {
                loadThemePreference();
                
                // 初始化测试日志
                addTestLog('system', 'INIT', '=== 耦合器控制器测试系统启动 ===');
                addTestLog('info', 'INIT', '系统初始化完成，等待用户操作');
                addTestLog('info', 'SYSTEM', `当前测试项目数量: ${testResults.value.length} 项`);
                
                nextTick(() => {
                    applyTheme();
                    if (window.lucide) {
                        window.lucide.createIcons();
                    }
                });
                
                const now = new Date();
                formData.testTime = now.toISOString().slice(0, 10);
                addTestLog('info', 'INIT', `测试时间已设置: ${now.toLocaleString()}`);
                
                try {
                    addTestLog('info', 'INIT', '正在获取当前用户信息...');
                    const response = await fetch('/api/coupler-controller-vue/get-current-user');
                    const data = await response.json();
                    
                    if (data.success && data.username) {
                        formData.tester = data.username;
                        addTestLog('success', 'INIT', `用户信息获取成功: ${data.username}`);
                    } else {
                        addTestLog('warning', 'INIT', '未能获取用户信息，请手动输入测试人员');
                    }
                } catch (error) {
                    Logger.error('获取用户信息失败:', error);
                    addTestLog('error', 'INIT', `获取用户信息失败: ${error.message}`);
                }
            });
            
            // 监听器
            // 监听工单号变化
            watch(() => formData.orderNumber, (newVal) => {
                if (newVal) {
                    debouncedQueryOrderInfo(newVal);
                }
            });

            // 监听产品状态变化 - 重新获取版本信息
            watch(() => formData.productStatus, () => {
                // 清空自动获取的版本信息
                formData.autoVersion = '';
                formData.autoDate = '';
                
                // 如果有产品SN号，重新获取版本信息
                if (['new', 'used', 'refurbished'].includes(formData.productStatus) && formData.productSN) {
                    autoFetchVersionInfo(formData.productSN);
                }
            });
            
            return {
                // 响应式数据
                loading,
                requiresPcbaCheck,
                basicInfoCollapsed,
                showTestLog,
                autoScroll,
                isDarkMode,
                showConfirmDialog,
                confirmAction,
                testLogs,
                currentTestIndex,
                testRunning,
                autoSubmitEnabled,
                formData,
                testResults,
                selectAll,
                rules,
                formRef,
                productStatusOptions,

                // 计算属性
                passedTests,
                failedTests,
                totalTests,
                testProgress,
                overallResult,
                isVersionConsistent,

                // 样式计算属性
                toolbarClasses,
                testSectionClasses,
                progressBarStyle,

                // 测试日志方法 - 企业级日志系统
                addTestLog,
                runAutoTest,
                stopTest,
                getLogLevelClass,
                getLogMessageClass,
                clearTestLogs,
                
                // 新增性能优化和日志管理
                filteredLogs,
                exportLogs,
                toggleLogLevel,
                logConfig,

                // 测试项目操作方法
                setTestResult,
                clearAllResults,
                setAllPass,
                getTestItemIconClass,

                // 基础功能方法
                toggleBasicInfo,
                toggleTheme,
                applyTheme,
                loadThemePreference,
                submitForm,
                
                // 业务逻辑功能
                queryOrderInfo,
                checkSN,
                autoFetchVersionInfo,
                focusToField,
                
                // 增强的错误处理功能
                handleValidationErrors,
                fieldNameMap,
                
                // 自动提交功能
                performAutoValidation,
                triggerAutoSubmitIfEnabled,
                
                // 输入处理功能
                handleOrderNumberInput,
                handleProductSNInput
            };
        },
        
        template: `
        <div class="coupler-controller__main gradient-bg">
            <!-- 顶部工具栏 -->
            <div :class="toolbarClasses">
                <div class="coupler-controller__toolbar-container">
                    <div class="coupler-controller__toolbar-left">
                        <!-- Logo和标题 -->
                        <div class="coupler-controller__toolbar-brand">
                            <div class="relative">
                                <div class="coupler-controller__icon coupler-controller__icon--blue rounded-xl shadow-lg">
                                    <i data-lucide="cpu" class="w-6 h-6 text-white"></i>
                                </div>
                                <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white animate-pulse"></div>
                            </div>
                            <div>
                                <h1 class="coupler-controller__title-main" :class="isDarkMode ? 'text-white' : 'text-gray-900'">耦合器控制器测试系统</h1>
                                <p class="coupler-controller__text-status" :class="isDarkMode ? 'text-blue-300' : 'text-blue-600'">Professional Coupler Testing Platform v2.1</p>
                            </div>
                        </div>
                        
                        <!-- 分隔线 -->
                        <div class="h-8 w-px bg-gray-300"></div>
                        
                        <!-- 版本信息状态 -->
                        <div class="coupler-controller__toolbar-status">
                            <div class="relative">
                                <div :class="['w-3 h-3 rounded-full shadow-lg status-indicator', isVersionConsistent ? 'bg-green-500 status-connected' : 'bg-yellow-500 status-disconnected']"></div>
                            </div>
                            <div>
                                <span class="text-sm font-medium theme-text-primary">
                                    {{ isVersionConsistent ? '版本一致' : '版本检查' }}
                                </span>
                                <p class="text-xs" :class="isDarkMode ? 'text-gray-300' : 'text-gray-600'">
                                    {{ formData.couplerVersion ? '版本已设置' : '请设置版本' }}
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="coupler-controller__toolbar-actions">
                        <!-- 主题切换按钮 -->
                        <el-button 
                            @click="toggleTheme"
                            class="theme-toggle-btn"
                            :title="isDarkMode ? '切换到浅色主题' : '切换到深色主题'"
                        >
                            <i :data-lucide="isDarkMode ? 'moon' : 'sun'" class="w-5 h-5"></i>
                        </el-button>
                        
                        <!-- 自动提交开关 -->
                        <div class="flex items-center gap-2 px-3 py-1 rounded-lg bg-opacity-20 backdrop-blur-sm" 
                             :class="isDarkMode ? 'bg-gray-700' : 'bg-white'"
                             style="border: 1px solid var(--coupler-card-border);">
                            <span class="text-sm font-medium" :class="isDarkMode ? 'text-gray-300' : 'text-gray-700'">
                                自动提交
                            </span>
                            <el-switch
                                v-model="autoSubmitEnabled"
                                :active-color="isDarkMode ? '#00d4ff' : '#2563eb'"
                                :inactive-color="isDarkMode ? '#4b5563' : '#d1d5db'"
                                size="small"
                                @change="(val) => addTestLog('info', 'AUTO_SUBMIT', '自动提交功能已' + (val ? '开启' : '关闭'))"
                            />
                        </div>
                        
                        <el-button 
                            v-if="!testRunning"
                            type="success"
                            @click="runAutoTest"
                            class="shadow-lg"
                        >
                            <i data-lucide="play" class="w-4 h-4 mr-2"></i>
                            自动测试
                        </el-button>
                        
                        <el-button 
                            v-else
                            type="danger"
                            @click="stopTest"
                            class="shadow-lg"
                        >
                            <i data-lucide="square" class="w-4 h-4 mr-2"></i>
                            停止测试
                        </el-button>
                        
                        <el-button 
                            type="primary"
                            :loading="loading"
                            @click="submitForm"
                            class="shadow-lg"
                        >
                            <i data-lucide="sparkles" class="w-4 h-4 mr-2"></i>
                            提交测试
                        </el-button>
                    </div>
                </div>
            </div>
            
            <!-- 主内容区域 -->
            <div class="coupler-controller__main-content main-content-area">
                <!-- 左侧表单区域 -->
                <div class="coupler-controller__form-section form-section">
                    <el-form 
                        ref="formRef" 
                        :model="formData" 
                        :rules="rules" 
                        @submit.prevent="submitForm"
                    >
                        <div class="coupler-controller__space-y">
                            <!-- 基本信息卡片 -->
                            <div class="coupler-controller__card theme-card glass-effect card-hover">
                                <div class="p-6 cursor-pointer" @click="toggleBasicInfo">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-2">
                                            <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                                                <i data-lucide="shield" class="w-4 h-4 text-white"></i>
                                            </div>
                                            <span class="coupler-controller__title-section theme-text-primary">基本信息</span>
                                        </div>
                                        <i :data-lucide="basicInfoCollapsed ? 'chevron-down' : 'chevron-up'" class="w-5 h-5 text-blue-300"></i>
                                    </div>
                                </div>
                                
                                <!-- 基本信息表单 -->
                                <div class="coupler-controller__form-content">
                                    <!-- 折叠状态 -->
                                    <div v-if="basicInfoCollapsed" class="coupler-controller__form-collapsed">
                                        <!-- 加工单号、产品型号 -->
                                        <div class="coupler-controller__grid-2">
                                            <div class="coupler-controller__field-group">
                                                <label class="coupler-controller__field-label">加工单号</label>
                                                <el-input
                                                    v-model="formData.orderNumber"
                                                    @input="handleOrderNumberInput"
                                                    placeholder="输入工单号自动查询"
                                                    class="h-10"
                                                ></el-input>
                                            </div>
                                            <div class="coupler-controller__field-group">
                                                <label class="coupler-controller__field-label">产品型号</label>
                                                <el-input
                                                    v-model="formData.productModel"
                                                    class="h-10"
                                                ></el-input>
                                            </div>
                                        </div>
                                        <!-- 产品SN号、SN字节数 -->
                                        <div class="coupler-controller__grid-2">
                                            <div class="coupler-controller__field-group">
                                                <label class="coupler-controller__field-label">产品SN号 *</label>
                                                <el-input
                                                    v-model="formData.productSN"
                                                    @input="handleProductSNInput"
                                                    @keyup.enter.prevent="checkSN(formData.productSN)"
                                                    @blur="checkSN(formData.productSN)"
                                                    placeholder="请输入产品SN号"
                                                    class="h-10"
                                                ></el-input>
                                            </div>
                                            <div class="coupler-controller__field-group">
                                                <label class="coupler-controller__field-label">SN字节数 *</label>
                                                <el-input
                                                    v-model="formData.snByteCount"
                                                    type="number"
                                                    class="h-10"
                                                ></el-input>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 展开状态 -->
                                    <transition name="collapse">
                                        <div v-show="!basicInfoCollapsed" class="coupler-controller__form-expanded">
                                            <!-- 第一行：测试人员、测试时间、批次号 -->
                                            <div class="coupler-controller__grid-3">
                                                <el-form-item label="测试人员" prop="tester">
                                                    <el-input v-model="formData.tester"></el-input>
                                                </el-form-item>
                                                <el-form-item label="测试时间">
                                                    <el-input 
                                                        v-model="formData.testTime" 
                                                        type="date">
                                                    </el-input>
                                                </el-form-item>
                                                <el-form-item label="批次号">
                                                    <el-input v-model="formData.batchNumber"></el-input>
                                                </el-form-item>
                                            </div>
                                            
                                            <!-- 第二行：产品编码、生产数量、SN字节数 -->
                                            <div class="coupler-controller__grid-3">
                                                <el-form-item label="产品编码" prop="productCode">
                                                    <el-input v-model="formData.productCode"></el-input>
                                                </el-form-item>
                                                <el-form-item label="生产数量" prop="productionQuantity">
                                                    <el-input v-model="formData.productionQuantity"></el-input>
                                                </el-form-item>
                                                <el-form-item label="SN字节数" prop="snByteCount">
                                                    <el-input v-model="formData.snByteCount" type="number"></el-input>
                                                </el-form-item>
                                            </div>
                                            
                                            <!-- 第三行：加工单号、产品型号 -->
                                            <div class="coupler-controller__grid-2">
                                                <el-form-item label="加工单号" prop="orderNumber">
                                                    <el-input 
                                                        v-model="formData.orderNumber"
                                                        @input="handleOrderNumberInput"
                                                        placeholder="输入工单号自动查询">
                                                    </el-input>
                                                </el-form-item>
                                                <el-form-item label="产品型号" prop="productModel">
                                                    <el-input v-model="formData.productModel"></el-input>
                                                </el-form-item>
                                            </div>
                                            
                                            <!-- 第四行：产品SN号、产品状态 -->
                                            <div class="coupler-controller__grid-2">
                                                <el-form-item label="产品SN号" prop="productSN">
                                                    <el-input 
                                                        v-model="formData.productSN"
                                                        @input="handleProductSNInput"
                                                        @keyup.enter.prevent="checkSN(formData.productSN)"
                                                        @blur="checkSN(formData.productSN)"
                                                        placeholder="请输入产品SN号">
                                                    </el-input>
                                                </el-form-item>
                                                <el-form-item label="产品状态" prop="productStatus">
                                                    <el-select v-model="formData.productStatus" class="w-full" placeholder="请选择产品状态">
                                                        <el-option 
                                                            v-for="option in productStatusOptions" 
                                                            :key="option.value"
                                                            :label="option.label" 
                                                            :value="option.value"
                                                            :disabled="option.disabled">
                                                        </el-option>
                                                    </el-select>
                                                </el-form-item>
                                            </div>
                                            
                                            <!-- 第五行：备注 -->
                                            <el-form-item label="备注">
                                                <el-input 
                                                    v-model="formData.remarks" 
                                                    type="textarea" 
                                                    :rows="2" 
                                                    placeholder="请输入备注信息">
                                                </el-input>
                                            </el-form-item>
                                        </div>
                                    </transition>
                                </div>
                            </div>
                            
                            <!-- 版本信息卡片 -->
                            <div class="coupler-controller__card theme-card glass-effect card-hover">
                                <div class="coupler-controller__card-content">
                                    <div class="coupler-controller__card-header">
                                        <div class="coupler-controller__card-title">
                                            <div class="coupler-controller__icon coupler-controller__icon--green">
                                                <i data-lucide="git-branch" class="w-4 h-4 text-white"></i>
                                            </div>
                                            <span class="coupler-controller__title-section theme-text-primary">版本信息</span>
                                        </div>
                                        <div v-if="!isVersionConsistent" class="text-yellow-600 text-sm">
                                            <i data-lucide="alert-triangle" class="w-4 h-4 inline mr-1"></i>
                                            版本不一致
                                        </div>
                                    </div>

                                    <div class="coupler-controller__device-content">
                                        <!-- 自动获取版本信息 -->
                                        <div class="coupler-controller__grid-2">
                                            <div class="coupler-controller__field-group">
                                                <label class="coupler-controller__field-label">自动获取版本</label>
                                                <el-input 
                                                    v-model="formData.autoVersion" 
                                                    readonly 
                                                    placeholder="SN输入后自动获取"
                                                    class="bg-gray-50">
                                                </el-input>
                                            </div>
                                            <div class="coupler-controller__field-group">
                                                <label class="coupler-controller__field-label">自动获取日期</label>
                                                <el-input 
                                                    v-model="formData.autoDate" 
                                                    readonly 
                                                    placeholder="SN输入后自动获取"
                                                    class="bg-gray-50">
                                                </el-input>
                                            </div>
                                        </div>

                                        <!-- 手动输入版本信息 -->
                                        <div class="coupler-controller__grid-2">
                                            <el-form-item label="耦合器软件版本" prop="couplerVersion" class="coupler-top-label">
                                                <el-input 
                                                    v-model="formData.couplerVersion" 
                                                    placeholder="请输入耦合器软件版本"
                                                ></el-input>
                                            </el-form-item>
                                            <el-form-item label="耦合器构建日期" prop="couplerBuildDate" class="coupler-top-label">
                                                <el-input 
                                                    v-model="formData.couplerBuildDate" 
                                                    placeholder="请输入耦合器构建日期"
                                                ></el-input>
                                            </el-form-item>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-form>
                </div>
                
                <!-- 右侧测试区域 -->
                <div :class="testSectionClasses">
                    <div class="space-y-6">
                        <!-- 测试进度卡片 -->
                        <div class="coupler-controller__card theme-card glass-effect card-hover">
                            <div class="coupler-controller__card-content">
                                <div class="coupler-controller__progress-header">
                                    <div class="coupler-controller__card-title">
                                        <div class="coupler-controller__icon coupler-controller__icon--purple">
                                            <i data-lucide="target" class="w-4 h-4 text-white"></i>
                                        </div>
                                        <span class="coupler-controller__title-section theme-text-primary">测试进度</span>
                                    </div>
                                    <div class="coupler-controller__progress-tabs">
                                        <el-button
                                            size="small"
                                            :type="!showTestLog ? 'primary' : ''"
                                            @click="showTestLog = false"
                                            class="h-8 text-xs"
                                        >
                                            进度视图
                                        </el-button>
                                        <el-button
                                            size="small"
                                            :type="showTestLog ? 'primary' : ''"
                                            @click="showTestLog = true"
                                            class="h-8 text-xs"
                                        >
                                            测试日志
                                        </el-button>
                                    </div>
                                </div>

                                <!-- 进度条 -->
                                <div class="mb-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-sm theme-text-secondary">总体进度</span>
                                        <span class="text-sm font-mono font-semibold theme-text-primary">{{ Math.round(testProgress) }}%</span>
                                    </div>
                                    <div class="custom-progress">
                                        <div class="custom-progress-bar coupler-controller__progress-bar" :style="progressBarStyle"></div>
                                    </div>
                                </div>

                                <!-- 进度视图 -->
                                <div v-if="!showTestLog">
                                    <div class="coupler-controller__progress-stats">
                                        <div class="coupler-controller__stat-card coupler-controller__stat-card--success">
                                            <div class="coupler-controller__stat-value text-green-600">{{ passedTests }}</div>
                                            <div class="coupler-controller__stat-label text-green-600">通过</div>
                                        </div>
                                        <div class="coupler-controller__stat-card coupler-controller__stat-card--danger">
                                            <div class="coupler-controller__stat-value text-red-600">{{ failedTests }}</div>
                                            <div class="coupler-controller__stat-label text-red-600">失败</div>
                                        </div>
                                        <div class="coupler-controller__stat-card coupler-controller__stat-card--neutral">
                                            <div class="coupler-controller__stat-value text-gray-600">{{ totalTests - passedTests - failedTests }}</div>
                                            <div class="coupler-controller__stat-label text-gray-600">待测</div>
                                        </div>
                                        <div :class="[
                                            'coupler-controller__stat-card',
                                            overallResult === 'PASS' ? 'coupler-controller__stat-card--success' :
                                            overallResult === 'NG' ? 'coupler-controller__stat-card--danger' :
                                            'coupler-controller__stat-card--neutral'
                                        ]">
                                            <div :class="[
                                                'coupler-controller__stat-value',
                                                overallResult === 'PASS' ? 'text-green-600' :
                                                overallResult === 'NG' ? 'text-red-600' :
                                                'text-gray-600'
                                            ]">{{ overallResult || '--' }}</div>
                                            <div :class="[
                                                'coupler-controller__stat-label',
                                                overallResult === 'PASS' ? 'text-green-600' :
                                                overallResult === 'NG' ? 'text-red-600' :
                                                'text-gray-600'
                                            ]">结果</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 测试日志视图 - 水平布局 -->
                                <div v-else class="coupler-controller__log-layout">
                                    <!-- 左侧统计信息 (20%) - 4行垂直布局 -->
                                    <div class="coupler-controller__log-stats">
                                        <!-- 通过 -->
                                        <div class="coupler-controller__compact-stat coupler-controller__compact-stat--success">
                                            <span>通过</span>
                                            <span>{{ passedTests }}</span>
                                        </div>
                                        
                                        <!-- 失败 -->
                                        <div class="coupler-controller__compact-stat coupler-controller__compact-stat--danger">
                                            <span>失败</span>
                                            <span>{{ failedTests }}</span>
                                        </div>
                                        
                                        <!-- 待测 -->
                                        <div class="coupler-controller__compact-stat coupler-controller__compact-stat--neutral">
                                            <span>待测</span>
                                            <span>{{ totalTests - passedTests - failedTests }}</span>
                                        </div>
                                        
                                        <!-- 结果 -->
                                        <div :class="[
                                            'coupler-controller__compact-stat',
                                            overallResult === 'PASS' ? 'coupler-controller__compact-stat--success' :
                                            overallResult === 'NG' ? 'coupler-controller__compact-stat--danger' :
                                            'coupler-controller__compact-stat--neutral'
                                        ]">
                                            <span>结果</span>
                                            <span>{{ overallResult || '--' }}</span>
                                        </div>
                                    </div>
                                    
                                    <!-- 右侧测试日志 (80%) -->
                                    <div class="coupler-controller__log-content">
                                        <div class="coupler-controller__log-controls">
                                            <div class="coupler-controller__log-indicators">
                                                <span class="text-gray-500">测试日志</span>
                                                <!-- 日志级别过滤按钮 -->
                                                <div 
                                                    v-for="level in logConfig.levels" 
                                                    :key="level"
                                                    class="coupler-controller__log-indicator cursor-pointer"
                                                    @click="toggleLogLevel(level)"
                                                    :class="{ 'opacity-50': !logConfig.enabledLevels.value.includes(level) }"
                                                >
                                                    <div :class="['coupler-controller__log-dot', \`coupler-controller__log-dot--\${level}\`]"></div>
                                                    <span :class="[
                                                        level === 'success' ? 'text-green-600' :
                                                        level === 'error' ? 'text-red-600' :
                                                        level === 'warning' ? 'text-yellow-600' :
                                                        level === 'info' ? 'text-blue-600' :
                                                        'text-purple-600'
                                                    ]">{{ level.toUpperCase() }}</span>
                                                </div>
                                            </div>
                                            <div class="flex items-center space-x-1">
                                                <el-button
                                                    size="small"
                                                    @click="autoScroll = !autoScroll"
                                                    class="h-6 px-2 text-xs"
                                                >
                                                    {{ autoScroll ? '🔒 自动滚动' : '🔓 手动滚动' }}
                                                </el-button>
                                                <el-button 
                                                    size="small" 
                                                    @click="exportLogs" 
                                                    class="h-6 px-2 text-xs"
                                                    :disabled="filteredLogs.length === 0"
                                                >
                                                    导出
                                                </el-button>
                                                <el-button size="small" @click="clearTestLogs" class="h-6 px-2 text-xs">
                                                    清空
                                                </el-button>
                                            </div>
                                        </div>

                                        <div id="coupler-test-log-container" class="flex-1 log-container rounded-lg p-3 overflow-y-auto text-xs">
                                            <div v-if="filteredLogs.length === 0" class="flex items-center justify-center h-full text-gray-500">
                                                <div class="text-center">
                                                    <div class="text-2xl mb-2">📋</div>
                                                    <div>{{ testLogs.length === 0 ? '点击"自动测试"开始记录日志' : '当前过滤条件下无日志' }}</div>
                                                </div>
                                            </div>
                                            <div v-else class="space-y-1">
                                                <div
                                                    v-for="log in filteredLogs"
                                                    :key="log.id"
                                                    class="flex items-start space-x-2"
                                                >
                                                    <span class="text-gray-400 shrink-0">{{ log.timestamp }}</span>
                                                    <span :class="['shrink-0 font-semibold', getLogLevelClass(log.level)]">
                                                        [{{ log.category }}]
                                                    </span>
                                                    <div class="flex-1 min-w-0">
                                                        <div :class="getLogLevelClass(log.level)">{{ log.message }}</div>
                                                        <div v-if="log.details" class="text-gray-400 text-xs mt-1 ml-2">
                                                            └─ {{ log.details }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 测试项目卡片 -->
                        <div class="coupler-controller__card theme-card glass-effect card-hover">
                            <div class="coupler-controller__card-content">
                                <div class="coupler-controller__progress-header">
                                    <div class="coupler-controller__card-title">
                                        <div class="coupler-controller__icon coupler-controller__icon--indigo">
                                            <i data-lucide="layers" class="w-4 h-4 text-white"></i>
                                        </div>
                                        <span class="coupler-controller__title-section theme-text-primary">测试项目</span>
                                    </div>
                                    
                                    <!-- 全局操作按钮 -->
                                    <div class="flex gap-2">
                                        <el-button 
                                            size="small"
                                            type="danger"
                                            @click="clearAllResults"
                                            class="h-8 px-3 text-xs"
                                        >
                                            <i data-lucide="trash-2" class="w-3 h-3 mr-1"></i>
                                            清除
                                        </el-button>
                                        <el-button 
                                            size="small"
                                            type="success"
                                            @click="setAllPass"
                                            class="h-8 px-3 text-xs"
                                        >
                                            <i data-lucide="check-circle" class="w-3 h-3 mr-1"></i>
                                            全通过
                                        </el-button>
                                    </div>
                                </div>

                                <div class="coupler-controller__test-items">
                                    <div
                                        v-for="(item, index) in testResults"
                                        :key="item.name"
                                        class="coupler-controller__test-item test-item-bg"
                                    >
                                        <div class="coupler-controller__test-item-info">
                                            <div :class="[
                                                'coupler-controller__test-item-icon',
                                                item.result === 'pass' ? 'bg-green-100 text-green-600' :
                                                item.result === 'fail' ? 'bg-red-100 text-red-600' :
                                                'bg-gray-100 text-gray-500'
                                            ]">
                                                <i :data-lucide="item.icon" class="w-4 h-4"></i>
                                            </div>
                                            <div class="coupler-controller__test-item-details">
                                                <div class="coupler-controller__test-item-name">{{ item.name }}</div>
                                                <div class="coupler-controller__test-item-meta">
                                                    <span>{{ item.category }}</span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="coupler-controller__test-item-actions">
                                            <!-- Pass State -->
                                            <el-tag v-if="item.result === 'pass'" type="success" size="small">
                                                <i data-lucide="check-circle"></i>
                                                <span>通过</span>
                                            </el-tag>

                                            <!-- Fail State -->
                                            <el-tag v-else-if="item.result === 'fail'" type="danger" size="small">
                                                <i data-lucide="x-circle"></i>
                                                <span>失败</span>
                                            </el-tag>

                                            <!-- Pending State -->
                                            <el-tag v-else type="info" size="small">
                                                <i data-lucide="clock" style="vertical-align: middle;"></i>
                                                <span style="vertical-align: middle;">待测</span>
                                            </el-tag>

                                            <!-- Action Buttons -->
                                            <div class="coupler-controller__test-item-buttons">
                                                <el-button
                                                    size="small"
                                                    :type="item.result === 'pass' ? 'success' : ''"
                                                    @click="setTestResult(index, 'pass')"
                                                    class="h-7 px-3 text-xs"
                                                >
                                                    通过
                                                </el-button>
                                                <el-button
                                                    size="small"
                                                    :type="item.result === 'fail' ? 'danger' : ''"
                                                    @click="setTestResult(index, 'fail')"
                                                    class="h-7 px-3 text-xs"
                                                >
                                                    失败
                                                </el-button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        `
    };

    // 创建并挂载Vue应用
    const app = createApp(CouplerControllerVueApp);
    app.use(ElementPlus);

    // 注册Element Plus图标
    if (window.ElementPlusIconsVue) {
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }
    }

    // 挂载应用
    const mountApp = () => {
        try {
            const container = document.getElementById('coupler-module-vue-app-container');
            if (!container) {
                Logger.error('Coupler module Vue app container not found');
                return;
            }
            
            container.innerHTML = '';
            
            app.mount('#coupler-module-vue-app-container');
            Logger.log('Coupler module Vue app mounted successfully');
            
            window.currentCouplerControllerVueApp = app;
        } catch (error) {
            Logger.error('Failed to mount Coupler Controller Vue app:', error);
        }
    };

    mountApp();
})(); 