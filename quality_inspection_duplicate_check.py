"""
产品重复检查API接口实现 - 基于实际数据库表结构

基于数据库表: product_inspection_status, products, quality_inspection_work_orders
文件: quality_inspection_duplicate_check.py
"""

from flask import Flask, request, jsonify, current_app
import pymysql
import logging
import traceback
from datetime import datetime
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('quality_inspection.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class QualityInspectionDB:
    """质检数据库操作类"""
    
    def __init__(self, app=None):
        self.app = app
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化数据库配置"""
        app.config.setdefault('MYSQL_HOST', 'localhost')
        app.config.setdefault('MYSQL_PORT', 3306)
        app.config.setdefault('MYSQL_USER', 'root')
        app.config.setdefault('MYSQL_PASSWORD', '')
        app.config.setdefault('MYSQL_DB', 'kmlc_plc')
        app.config.setdefault('MYSQL_CHARSET', 'utf8mb4')
        
        # 连接池配置
        app.config.setdefault('MYSQL_POOL_SIZE', 10)
        app.config.setdefault('MYSQL_POOL_RECYCLE', 3600)
    
    def get_connection(self):
        """获取数据库连接"""
        try:
            connection = pymysql.connect(
                host=current_app.config['MYSQL_HOST'],
                port=current_app.config['MYSQL_PORT'],
                user=current_app.config['MYSQL_USER'],
                password=current_app.config['MYSQL_PASSWORD'],
                database=current_app.config['MYSQL_DB'],
                charset=current_app.config['MYSQL_CHARSET'],
                autocommit=True,
                connect_timeout=5,  # 5秒连接超时
                read_timeout=3,     # 3秒读取超时
                write_timeout=3,    # 3秒写入超时
                cursorclass=pymysql.cursors.DictCursor
            )
            return connection
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            raise


# 初始化数据库实例
db = QualityInspectionDB()


def check_product_duplicate():
    """
    检查产品是否已在当前阶段和角色中被最终检验过
    
    URL: GET /api/quality-inspection/check-product-duplicate
    
    查询参数:
    - work_order_id: 工单ID (必需)
    - serial_no: 产品序列号 (必需)  
    - stage: 检验阶段，如 assembly/test/packaging (必需)
    - inspector_role: 检验角色，如 first/self/ipqc (必需)
    
    返回格式:
    {
        "success": boolean,
        "already_inspected": boolean,  // true表示已检验过
        "message": string,
        "debug_info": object  // 调试信息（可选）
    }
    """
    try:
        # 获取查询参数
        work_order_id = request.args.get('work_order_id')
        serial_no = request.args.get('serial_no')
        stage = request.args.get('stage')
        inspector_role = request.args.get('inspector_role')
        
        # 参数验证
        if not all([work_order_id, serial_no, stage, inspector_role]):
            logger.warning(
                f"参数不完整: work_order_id={work_order_id}, "
                f"serial_no={serial_no}, stage={stage}, inspector_role={inspector_role}"
            )
            return jsonify({
                'success': True,  # 注意：参数错误时仍返回success=True
                'already_inspected': False,  # 参数错误时返回False，允许继续操作
                'message': '参数不完整，跳过重复检查'
            }), 200
        
        # 参数格式验证
        try:
            work_order_id = int(work_order_id)
        except ValueError:
            logger.warning(f"工单ID格式无效: {work_order_id}")
            return jsonify({
                'success': True,
                'already_inspected': False,
                'message': '工单ID格式无效，跳过重复检查'
            }), 200
        
        # 验证stage和inspector_role的有效值
        valid_stages = ['assembly', 'test', 'packaging']
        valid_roles = ['first', 'self', 'ipqc']
        
        if stage not in valid_stages:
            logger.warning(f"无效的检验阶段: {stage}")
            return jsonify({
                'success': True,
                'already_inspected': False,
                'message': f'无效的检验阶段: {stage}'
            }), 200
            
        if inspector_role not in valid_roles:
            logger.warning(f"无效的检验角色: {inspector_role}")
            return jsonify({
                'success': True,
                'already_inspected': False,
                'message': f'无效的检验角色: {inspector_role}'
            }), 200
        
        # 记录查询请求（用于调试）
        logger.info(
            f"检查产品重复: work_order_id={work_order_id}, "
            f"serial_no={serial_no}, stage={stage}, role={inspector_role}"
        )
        
        # 执行重复检查查询
        result = check_inspection_exists(work_order_id, serial_no, stage, inspector_role)
        
        # 构建响应
        response_data = {
            'success': True,
            'already_inspected': result['already_inspected'],
            'message': result['message']
        }
        
        # 在开发/调试模式下添加调试信息
        if current_app.debug:
            response_data['debug_info'] = {
                'query_time': result.get('query_time'),
                'found_records': result.get('found_records', 0),
                'product_id': result.get('product_id'),
                'inspection_time': result.get('inspection_time')
            }
        
        logger.info(f"重复检查结果: already_inspected={result['already_inspected']}")
        
        return jsonify(response_data), 200
        
    except Exception as e:
        # 记录错误但返回允许继续操作的响应
        logger.error(f"检查产品重复失败: {str(e)}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        
        # 关键：错误时返回success=True, already_inspected=False
        # 这确保前端不会因为后端错误而阻塞用户操作
        return jsonify({
            'success': True,
            'already_inspected': False,  # 错误时返回false，允许继续操作
            'message': '检查失败，允许继续操作',
            'error_detail': str(e) if current_app.debug else None
        }), 200  # 返回200状态码，避免前端捕获为网络错误


def check_inspection_exists(work_order_id, serial_no, stage, inspector_role):
    """
    实际的数据库查询逻辑
    
    查询逻辑:
    1. 通过 serial_no 和 work_order_id 在 products 表中找到 product_id
    2. 在 product_inspection_status 表中查询是否存在最终提交的检验记录
    
    返回: dict - {
        'already_inspected': boolean,
        'message': string,
        'query_time': float,
        'found_records': int,
        'product_id': int,
        'inspection_time': datetime
    }
    """
    connection = None
    start_time = datetime.now()
    
    try:
        connection = db.get_connection()
        
        with connection.cursor() as cursor:
            # 查询SQL - 联合查询产品表和检验状态表
            query_sql = """
                SELECT 
                    pis.id,
                    pis.product_id,
                    pis.inspector,
                    pis.inspection_time,
                    pis.submission_type,
                    p.serial_no
                FROM product_inspection_status pis
                INNER JOIN products p ON pis.product_id = p.id
                WHERE pis.work_order_id = %s 
                    AND p.serial_no = %s 
                    AND pis.stage = %s 
                    AND pis.inspector_role = %s
                    AND pis.submission_type = 'final'
                LIMIT 1
            """
            
            # 执行查询
            cursor.execute(query_sql, (work_order_id, serial_no, stage, inspector_role))
            result = cursor.fetchone()
            
            # 计算查询时间
            query_time = (datetime.now() - start_time).total_seconds()
            
            if result:
                # 找到最终提交的检验记录
                logger.info(
                    f"发现重复检验记录: product_id={result['product_id']}, "
                    f"inspector={result['inspector']}, "
                    f"inspection_time={result['inspection_time']}"
                )
                
                return {
                    'already_inspected': True,
                    'message': f'产品已在{stage}阶段由{inspector_role}完成最终检验',
                    'query_time': query_time,
                    'found_records': 1,
                    'product_id': result['product_id'],
                    'inspection_time': result['inspection_time'].isoformat() if result['inspection_time'] else None
                }
            else:
                # 未找到最终提交的检验记录
                logger.info(f"未发现重复检验记录")
                
                return {
                    'already_inspected': False,
                    'message': '产品未在当前阶段完成最终检验',
                    'query_time': query_time,
                    'found_records': 0,
                    'product_id': None,
                    'inspection_time': None
                }
                
    except pymysql.Error as e:
        logger.error(f"数据库查询错误: {str(e)}")
        return {
            'already_inspected': False,
            'message': f'数据库查询失败: {str(e)}',
            'query_time': (datetime.now() - start_time).total_seconds(),
            'found_records': 0,
            'product_id': None,
            'inspection_time': None
        }
    except Exception as e:
        logger.error(f"查询执行失败: {str(e)}")
        return {
            'already_inspected': False,
            'message': f'查询执行失败: {str(e)}',
            'query_time': (datetime.now() - start_time).total_seconds(),
            'found_records': 0,
            'product_id': None,
            'inspection_time': None
        }
    finally:
        if connection:
            connection.close()


def add_duplicate_check_index():
    """
    添加重复检查查询优化索引
    这个函数应该在数据库初始化时调用，或者通过SQL脚本执行
    """
    connection = None
    try:
        connection = db.get_connection()
        
        with connection.cursor() as cursor:
            # 检查索引是否已存在
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM information_schema.statistics 
                WHERE table_schema = DATABASE() 
                AND table_name = 'product_inspection_status' 
                AND index_name = 'idx_duplicate_check'
            """)
            
            result = cursor.fetchone()
            
            if result['count'] == 0:
                # 创建重复检查优化索引
                index_sql = """
                    CREATE INDEX idx_duplicate_check 
                    ON product_inspection_status (work_order_id, stage, inspector_role, submission_type)
                """
                cursor.execute(index_sql)
                logger.info("已创建重复检查优化索引: idx_duplicate_check")
            else:
                logger.info("重复检查优化索引已存在")
                
            # 检查products表的serial_no索引
            cursor.execute("""
                SELECT COUNT(*) as count
                FROM information_schema.statistics 
                WHERE table_schema = DATABASE() 
                AND table_name = 'products' 
                AND index_name = 'idx_products_work_order_serial'
            """)
            
            result = cursor.fetchone()
            
            if result['count'] == 0:
                # 创建products表复合索引
                index_sql = """
                    CREATE INDEX idx_products_work_order_serial 
                    ON products (work_order_id, serial_no)
                """
                cursor.execute(index_sql)
                logger.info("已创建products表优化索引: idx_products_work_order_serial")
            else:
                logger.info("products表优化索引已存在")
                
    except Exception as e:
        logger.error(f"创建索引失败: {str(e)}")
    finally:
        if connection:
            connection.close()


# ===== Flask应用集成示例 =====

def create_app():
    """创建Flask应用"""
    app = Flask(__name__)
    
    # 数据库配置 - 请根据实际环境修改
    app.config.update({
        'MYSQL_HOST': 'localhost',
        'MYSQL_PORT': 3306,
        'MYSQL_USER': 'root',
        'MYSQL_PASSWORD': 'your_password',  # 请修改为实际密码
        'MYSQL_DB': 'kmlc_plc',
        'MYSQL_CHARSET': 'utf8mb4',
        'DEBUG': False  # 生产环境设置为False
    })
    
    # 初始化数据库
    db.init_app(app)
    
    # 注册路由
    @app.route('/api/quality-inspection/check-product-duplicate', methods=['GET'])
    def api_check_product_duplicate():
        return check_product_duplicate()
    
    # 健康检查接口
    @app.route('/api/health', methods=['GET'])
    def health_check():
        try:
            connection = db.get_connection()
            connection.close()
            return jsonify({
                'status': 'healthy',
                'database': 'connected',
                'timestamp': datetime.now().isoformat()
            }), 200
        except Exception as e:
            return jsonify({
                'status': 'unhealthy',
                'database': 'disconnected',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }), 500
    
    # 初始化数据库索引
    @app.before_first_request
    def init_database():
        add_duplicate_check_index()
    
    return app


# ===== 单独运行测试 =====
if __name__ == '__main__':
    app = create_app()
    
    # 开发模式运行
    app.config['DEBUG'] = True
    app.run(host='0.0.0.0', port=5000, debug=True)


# ===== 使用示例 =====
"""
# 启动服务器
python quality_inspection_duplicate_check.py

# 测试API调用
curl "http://localhost:5000/api/quality-inspection/check-product-duplicate?work_order_id=1&serial_no=TEST001&stage=assembly&inspector_role=first"

# 健康检查
curl "http://localhost:5000/api/health"
"""

print("质检重复检查API服务已生成")
print("请根据实际数据库配置修改连接参数")
print("建议在生产环境中使用WSGI服务器（如Gunicorn）运行")