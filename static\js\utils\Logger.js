(function(window) {
    'use strict';

    // 全局日志记录器工具
    // 手动将 DEBUG 设置为 false 来禁用所有页面的控制台输出
    const GlobalLogger = {
        DEBUG: true, // <-- 在此手动切换所有页面的日志开关 (true = 开启, false = 关闭)
        log: function(...args) {
            if (this.DEBUG) {
                console.log(...args);
            }
        },
        info: function(...args) {
            if (this.DEBUG) {
                console.info(...args);
            }
        },
        warn: function(...args) {
            if (this.DEBUG) {
                console.warn(...args);
            }
        },
        error: function(...args) {
            // 错误日志也可以根据需要进行控制
            if (this.DEBUG) {
                console.error(...args);
            }
        }
    };

    // 将 Logger 暴露到全局作用域
    window.Logger = GlobalLogger;

})(window); 