let ignoreNextHashChange = false;

function savePageState(page) {
    // 设置标志，表示接下来的 hashchange 是程序触发的
    ignoreNextHashChange = true;
    window.location.hash = page;
}

function loadPageState() {
    Logger.log('Loading page state, current hash:', window.location.hash);
    const hash = window.location.hash.slice(1); // 移除 # 符号

    if (hash) {
        Logger.log('Found hash:', hash);
        const menuItem = document.querySelector(`.menu-item[data-page="${hash}"]`);
        if (menuItem) {
            Logger.log('Found menu item for:', hash);
            const parentSubmenu = menuItem.closest('.submenu');
            if (parentSubmenu) {
                parentSubmenu.classList.add('active');
                const parentMenuItem = parentSubmenu.previousElementSibling;
                if (parentMenuItem) {
                    const chevron = parentMenuItem.querySelector('.fa-chevron-down');
                    if (chevron) {
                        chevron.classList.add('fa-rotate-180');
                    }
                }
            }

            menuItemsWithSubmenu.forEach(i => i.classList.remove('active'));
            document.querySelectorAll('.submenu .menu-item').forEach(i => i.classList.remove('active'));
            menuItem.classList.add('active');

            updateContent(hash);
            updateBreadcrumb(hash);
            const menuText = menuItem.querySelector('span').textContent.trim();
            addTag(menuText, hash);
            return true;
        }
    }

    return false;
}

const menuItems = document.querySelectorAll('.menu-item');
const content = document.getElementById('content');
const breadcrumb = document.querySelector('.breadcrumb');
const hamburger = document.querySelector('.hamburger');
const sidebar = document.querySelector('.sidebar-container');
const mainContainer = document.querySelector('.main-container');
const submenu = document.querySelector('.submenu');
const tagsViewWrapper = document.querySelector('.tags-view-wrapper');
const menuItemsWithSubmenu = document.querySelectorAll('.menu-item:not(.submenu .menu-item)');

menuItemsWithSubmenu.forEach(item => {
    item.addEventListener('click', () => {
        const submenu = item.nextElementSibling;
        if (submenu && submenu.classList.contains('submenu')) {
            submenu.classList.toggle('active');
            item.querySelector('.fa-chevron-down')?.classList.toggle('fa-rotate-180');
        } else {
            menuItemsWithSubmenu.forEach(i => i.classList.remove('active'));
            item.classList.add('active');
            const page = item.getAttribute('data-page');
            updateContent(page);
            updateBreadcrumb(page);
            addTag(item.querySelector('span').textContent.trim(), page);
            savePageState(page);
        }
    });
});

document.querySelectorAll('.submenu .menu-item').forEach(item => {
    item.addEventListener('click', (e) => {
        e.stopPropagation();
        menuItemsWithSubmenu.forEach(i => i.classList.remove('active'));
        document.querySelectorAll('.submenu .menu-item').forEach(i => i.classList.remove('active'));
        item.classList.add('active');
        const page = item.getAttribute('data-page');
        updateContent(page);
        updateBreadcrumb(page);
        addTag(item.querySelector('span').textContent.trim(), page);
        savePageState(page);
    });
});

hamburger.addEventListener('click', () => {
    sidebar.classList.toggle('collapsed');
    mainContainer.style.marginLeft = sidebar.classList.contains('collapsed') ? '64px' : 'var(--sidebar-width)';
    adjustFixedHeader();
});

function updateContent(page) {
    Logger.log('Updating content for page:', page);

    if (window.cleanupDocumentCenter) window.cleanupDocumentCenter();
    if (window.cleanupIOModule) window.cleanupIOModule();
    if (window.cleanupFaultCode) window.cleanupFaultCode();

    content.innerHTML = '';
    content.className = '';

    Logger.log('Loading page content:', page);

    switch(page) {
        case 'marking-record':
            content.innerHTML = '<div id="marking-record-content"></div>';
            loadCSS('../static/page_js_css/MarkingRecord.css');
            loadScript('../static/page_js_css/MarkingRecord.js', () => {
                initMarkingRecordPage();
            });
            break;
        case 'dashboard':
            content.innerHTML = '<div id="dashboard-content"></div>';
            loadCSS('../static/page_js_css/Dashboard.css');
            loadScript('../static/page_js_css/Dashboard.js', () => {
                initDashboard();
            });
            break;
        case 'cpu-controller':
            loadCPUControllerPage();
            break;
        case 'io-module':
            content.innerHTML = '<div id="io-module-content"></div>';
            loadCSS('../static/page_js_css/IOModule.css');
            loadScript('../static/page_js_css/IOModule.js', () => {
                initIOModulePage();
            });
            break;
        case 'io-module-vue':
            content.innerHTML = '<div id="io-module-vue-app-container"></div>';
            loadVueIOModulePage('io-module-vue', 'io-module-vue-app-container');
            break;
        case 'coupler':
            loadCouplerPage();
            break;
        case 'coupler-vue':
            content.innerHTML = '<div id="coupler-module-vue-app-container"></div>';
            loadVueCouplerPage('coupler-vue', 'coupler-module-vue-app-container');
            break;
        case 'cpu-controller-vue':
            content.innerHTML = '<div id="cpu-controller-vue-app-container"></div>';
            loadVueCPUControllerPage('cpu-controller-vue', 'cpu-controller-vue-app-container');
            break;
        case 'board-test':
            content.innerHTML = '<div id="board-test-content"></div>';
            loadCSS('../static/page_js_css/BoardTest.css');
            loadScript('../static/page_js_css/BoardTest.js', () => {
                initBoardTestPage();
            });
            break;
        case 'fault-entry':
            content.innerHTML = '<div id="fault-entry-content"></div>';
            loadCSS('../static/page_js_css/FaultEntry.css');
            loadScript('../static/page_js_css/FaultEntry.js', () => {
                initFaultEntryPage();
            });
            break;
        case 'fault-code':
            content.innerHTML = '<div id="fault-code-content"></div>';
            loadCSS('../static/page_js_css/FaultCode.css');
            loadScript('../static/page_js_css/FaultCode.js', () => {
                initFaultCodePage();
            });
            break;
        case 'document-center':
            content.innerHTML = '<div id="document-center-content"></div>';
            loadCSS('../static/page_js_css/DocumentCenter.css');
            loadScript('../static/page_js_css/DocumentCenter.js', () => {
                initDocumentCenter();
            });
            break;
        case 'barcode-comparison':
            content.innerHTML = '<div id="barcode-comparison-content"></div>';
            loadCSS('../static/page_js_css/barcode-comparison.css');
            loadScript('../static/page_js_css/barcode-comparison.js', () => {
                initBarcodeComparison();
            });
            break;
        case 'barcode-binding':
            content.innerHTML = '<div id="barcode-binding-content"></div>';
            loadCSS('../static/page_js_css/BarcodeBinding.css');
            loadScript('../static/page_js_css/BarcodeBinding.js', () => {
                initBarcodeBindingPage();
            });
            break;
        case 'sn-print-record':
            content.innerHTML = '<div id="sn-print-record-content"></div>';
            loadCSS('../static/page_js_css/SNPrintRecord.css');
            loadScript('../static/page_js_css/SNPrintRecord.js', () => {
                initSNPrintRecordPage();
            });
            break;
        case 'batch-query':
            content.innerHTML = '<div id="batch-query-content"></div>';
            loadCSS('../static/page_js_css/BatchQuery.css');
            loadScript('../static/page_js_css/BatchQuery.js', () => {
                initBatchQueryPage();
            });
            break;
        case 'data-statistics':
            content.innerHTML = '<h2>数据统计</h2><p>这里是数据统计的内容。</p>';
            break;
        case 'about':
            content.innerHTML = '<h2>关于</h2><p>这里是关于页面的内容。</p>';
            break;
        case 'fault-query':
            content.innerHTML = '<div id="fault-query-content"></div>';
            loadCSS('../static/page_js_css/FaultQuery.css');
            loadScript('../static/page_js_css/FaultQuery.js', () => {
                initFaultQueryPage();
            });
            break;
        case 'product-test-query':
            content.innerHTML = '<div id="product-test-query-content"></div>';
            loadCSS('../static/page_js_css/ProductTestQuery.css');
            loadScript('../static/page_js_css/ProductTestQuery.js', () => {
                initProductTestQueryPage();
            });
            break;
        case 'product-management':
            content.innerHTML = '<div id="product-management-content"></div>';
            loadCSS('../static/page_js_css/ProductManagement.css');
            loadScript('../static/page_js_css/ProductManagement.js', () => {
                initProductManagement();
            });
            break;
        case 'order-management':
            content.innerHTML = '<div id="order-management-content"></div>';
            loadCSS('../static/page_js_css/OrderManagement.css');
            loadScript('../static/page_js_css/OrderManagement.js', () => {
                initOrderManagement();
            });
            break;
        case 'shipment-barcode':
            content.innerHTML = '<div id="shipment-barcode-content"></div>';
            loadCSS('../static/page_js_css/ShipmentBarcode.css');
            loadScript('../static/page_js_css/ShipmentBarcode.js', () => {
                initShipmentBarcodePage();
            });
            break;
        case 'shipment-query':
            content.innerHTML = '<div id="shipment-query-content"></div>';
            loadCSS('../static/page_js_css/ShipmentQuery.css');
            loadScript('../static/page_js_css/ShipmentQuery.js', () => {
                initShipmentQueryPage();
            });
            break;
        case 'quality-inspection':
            content.innerHTML = '<h2>质检记录</h2><p>这里是质检记录的内容。</p>';
            break;
        case 'self-inspection':
            content.innerHTML = '<div id="self-inspection-content"></div>';
            loadCSS('../static/page_js_css/SelfInspection.css');

            // 先加载ScanSN.js，然后再加载SelfInspection.js
            loadScript('/static/page_js_css/ScanSN.js', function() {
                Logger.log('ScanSN.js loaded');
                loadScript('/static/page_js_css/SelfInspection.js', function() {
                    Logger.log('SelfInspection.js loaded');
                    if (typeof initSelfInspectionPage === 'function') {
                        initSelfInspectionPage();
                    } else {
                        Logger.error('initSelfInspectionPage function not found');
                    }
                });
            });
            break;
        case 'inspection-record':
            content.innerHTML = '<div id="inspection-record-content"></div>';
            loadCSS('../static/page_js_css/InspectionRecord.css');
            loadScript('/static/page_js_css/InspectionRecord.js', function() {
                initInspectionRecordPage();
            });
            break;
        case 'after-sales-query':
            content.innerHTML = '<div id="after-sales-query-content"></div>';
            loadCSS('../static/page_js_css/AfterSalesQuery.css');
            loadScript('../static/page_js_css/AfterSalesQuery.js', () => {
                initAfterSalesQueryPage();
            });
            break;
        case 'firmware-management':
            content.innerHTML = '<h2>固件管理</h2><p>这里是固件管理主页面的内容。请选择一个子菜单。</p>';
            break;
        case 'all-firmware':
            content.innerHTML = '<div id="all-firmware-app-container"></div>';
            loadVueFirmwarePage('all-firmware', 'all-firmware-app-container');
            break;
        case 'pending-firmware':
            content.innerHTML = '<div id="pending-firmware-app-container"></div>';
            loadVueFirmwarePage('pending-firmware', 'pending-firmware-app-container');
            break;
        case 'usage-record':
            content.innerHTML = '<div id="usage-record-app-container"></div>';
            loadVueFirmwarePage('usage-record', 'usage-record-app-container');
            break;
        case 'obsolete-firmware':
            content.innerHTML = '<div id="obsolete-firmware-app-container"></div>';
            loadVueFirmwarePage('obsolete-firmware', 'obsolete-firmware-app-container');
            break;
        case 'version-comparison':
            content.innerHTML = '<div id="version-comparison-app-container"></div>';
            loadVueFirmwarePage('version-comparison', 'version-comparison-app-container');
            break;
    }
}

function loadIOModulePage() {
    content.innerHTML = '<div id="io-module-content"></div>';
    loadCSS('../static/page_js_css/IOModule.css');
    loadScript('../static/page_js_css/IOModule.js', () => {
        initIOModulePage();
    });
}

function loadCPUControllerPage() {
    content.innerHTML = '<div id="cpu-controller-content"></div>';
    loadCSS('../static/page_js_css/CPUController.css');
    loadScript('../static/page_js_css/CPUController.js', () => {
        initCPUControllerPage();
    });
}

function loadCSS(url) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = url;
    document.head.appendChild(link);
}

function loadScript(url, callback) {
    const script = document.createElement('script');
    script.src = url;
    script.onload = callback;
    document.body.appendChild(script);
}

function updateBreadcrumb(page) {
    let breadcrumbHTML = `<span class="breadcrumb-item" data-page="dashboard">首页</span>`;

    if (page === 'dashboard') {
        breadcrumbHTML += `<span>/</span><span class="breadcrumb-item active">首页</span>`;
    } else if (['product-management', 'order-management'].includes(page)) {
        breadcrumbHTML += `<span>/</span><span class="breadcrumb-item" data-page="basic-info">基本信息</span>`;
        breadcrumbHTML += `<span>/</span><span class="breadcrumb-item active">${getPageTitle(page)}</span>`;
    } else if (['all-firmware', 'pending-firmware', 'obsolete-firmware', 'usage-record', 'version-comparison'].includes(page)) {
        breadcrumbHTML += `<span>/</span><span class="breadcrumb-item" data-page="firmware-management-main">固件管理</span>`;
        breadcrumbHTML += `<span>/</span><span class="breadcrumb-item active">${getPageTitle(page)}</span>`;
    } else if (['cpu-controller', 'cpu-controller-vue', 'io-module', 'io-module-vue', 'coupler', 'coupler-vue'].includes(page)) {
        breadcrumbHTML += `<span>/</span><span class="breadcrumb-item" data-page="product-test">成品测试</span>`;
        breadcrumbHTML += `<span>/</span><span class="breadcrumb-item active">${getPageTitle(page)}</span>`;
    } else if (page === 'sn-print-record') {
        breadcrumbHTML += `<span>/</span><span class="breadcrumb-item" data-page="laser-marking">激光打标</span>`;
        breadcrumbHTML += `<span>/</span><span class="breadcrumb-item active">${getPageTitle(page)}</span>`;
    } else if (['batch-query', 'fault-query', 'product-test-query', 'after-sales-query'].includes(page)) {
        breadcrumbHTML += `<span>/</span><span class="breadcrumb-item" data-page="data-statistics">数据统计</span>`;
        breadcrumbHTML += `<span>/</span><span class="breadcrumb-item active">${getPageTitle(page)}</span>`;
    } else {
        breadcrumbHTML += `<span>/</span><span class="breadcrumb-item active">${getPageTitle(page)}</span>`;
    }

    breadcrumb.innerHTML = breadcrumbHTML;

    document.querySelectorAll('.breadcrumb-item:not(.active)').forEach(item => {
        item.addEventListener('click', () => {
            const clickedPage = item.getAttribute('data-page');
            updateContent(clickedPage);
            updateBreadcrumb(clickedPage);
            addTag(item.textContent.trim(), clickedPage);

            menuItems.forEach(menuItem => {
                if (menuItem.getAttribute('data-page') === clickedPage) {
                    menuItems.forEach(i => i.classList.remove('active'));
                    menuItem.classList.add('active');
                }
            });
        });
    });
}

function capitalize(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
}

function addTag(name, page, isDashboard = false) {
    const existingTag = document.querySelector(`.tags-view-item[data-page="${page}"]`);
    if (existingTag) {
        activateTag(existingTag);
        if (!isDashboard) {  // 只在非首页时保存状态
            savePageState(page);
        }
        return;
    }

    const tag = document.createElement('span');
    tag.className = 'tags-view-item';
    tag.setAttribute('data-page', page);
    tag.innerHTML = `
        ${name}
        ${page !== 'dashboard' ? '<span class="el-icon-close">×</span>' : ''}
    `;

    // 如果是首页标签，插入到最前面
    if (isDashboard) {
        tagsViewWrapper.insertBefore(tag, tagsViewWrapper.firstChild);
    } else {
        tagsViewWrapper.appendChild(tag);
    }

    activateTag(tag);
    if (!isDashboard) {  // 只在非首页时保存状态
        savePageState(page);
    }

    if (page !== 'dashboard') {
        tag.querySelector('.el-icon-close').addEventListener('click', (e) => {
            e.stopPropagation();
            removeTag(tag);
        });
    }

    tag.addEventListener('click', () => {
        activateTag(tag);
        updateContent(page);
        updateBreadcrumb(page);
        if (!isDashboard) {  // 只在非首页时保存状态
            savePageState(page);
        }
    });
}

function activateTag(tag) {
    document.querySelectorAll('.tags-view-item').forEach(t => t.classList.remove('active'));
    tag.classList.add('active');
}

function removeTag(tag) {
    if (tag.getAttribute('data-page') === 'dashboard') {
        return; // 不允许删除首页标签
    }

    if (tag.classList.contains('active')) {
        const sibling = tag.previousElementSibling || tag.nextElementSibling;
        if (sibling) {
            activateTag(sibling);
            updateContent(sibling.getAttribute('data-page'));
            updateBreadcrumb(sibling.getAttribute('data-page'));
        }
    }
    tag.remove();
}

const fullscreenToggle = document.getElementById('fullscreen-toggle');
fullscreenToggle.addEventListener('click', () => {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
    } else {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        }
    }
});

const sizeSelect = document.getElementById('size-select');
sizeSelect.addEventListener('click', () => {
    document.body.classList.toggle('compact-layout');
});

const languageSelect = document.getElementById('language-select');
languageSelect.addEventListener('change', (e) => {
    Logger.log('Language changed to:', e.target.value);
});

const tagContextMenu = document.getElementById('tagContextMenu');
let currentTag = null;

document.addEventListener('contextmenu', function(e) {
    if (e.target.closest('.tags-view-item')) {
        e.preventDefault();
        currentTag = e.target.closest('.tags-view-item');
        tagContextMenu.style.display = 'block';
        tagContextMenu.style.left = e.pageX + 'px';
        tagContextMenu.style.top = e.pageY + 'px';
    }
});

document.addEventListener('click', function(e) {
    if (!e.target.closest('.context-menu')) {
        tagContextMenu.style.display = 'none';
    }
});

tagContextMenu.addEventListener('click', function(e) {
    const action = e.target.getAttribute('data-action');
    if (action && currentTag) {
        Logger.log('Context menu action:', action);
        switch (action) {
            case 'refresh':
                refreshTag(currentTag);
                break;
            case 'close':
                if (currentTag.getAttribute('data-page') !== 'dashboard') {
                    removeTag(currentTag);
                }
                break;
            case 'closeOthers':
                document.querySelectorAll('.tags-view-item').forEach(tag => {
                    if (tag !== currentTag && tag.getAttribute('data-page') !== 'dashboard') {
                        removeTag(tag);
                    }
                });
                break;
            case 'closeAll':
                document.querySelectorAll('.tags-view-item').forEach(tag => {
                    if (tag.getAttribute('data-page') !== 'dashboard') {
                        removeTag(tag);
                    }
                });
                break;
        }
        tagContextMenu.style.display = 'none';
    }
});

function refreshTag(tag) {
    const page = tag.getAttribute('data-page');
    Logger.log('Refreshing page:', page);

    tag.classList.add('refreshing');
    setTimeout(() => {
        tag.classList.remove('refreshing');
    }, 500);

    updateContent(page);
    updateBreadcrumb(page);

    Logger.log('刷新标签页完成:', tag.textContent.trim());
}

document.addEventListener('DOMContentLoaded', () => {
    Logger.log('DOM Content Loaded - Initial Setup');

    // 获取并显示用户信息
    fetchUserInfo();

    window.removeEventListener('hashchange', handleHashChange);

    initializePageState();

    window.addEventListener('hashchange', handleHashChange);

    adjustSidebar();
});

function initializePageState() {
    Logger.log('Initializing page state');
    Logger.log('Current hash:', window.location.hash);

    // 确保首页标签始终存在
    const dashboardTag = document.querySelector('.tags-view-item[data-page="dashboard"]');
    if (!dashboardTag) {
        addTag('首页', 'dashboard', true); // 添加 true 参数表示这是首页标签
    }

    const hash = window.location.hash.slice(1);

    if (hash) {
        const menuItem = document.querySelector(`.menu-item[data-page="${hash}"]`);
        if (menuItem) {
            Logger.log('Loading page from hash:', hash);

            const parentSubmenu = menuItem.closest('.submenu');
            if (parentSubmenu) {
                parentSubmenu.classList.add('active');
                const parentMenuItem = parentSubmenu.previousElementSibling;
                if (parentMenuItem) {
                    const chevron = parentMenuItem.querySelector('.fa-chevron-down');
                    if (chevron) {
                        chevron.classList.add('fa-rotate-180');
                    }
                }
            }

            menuItemsWithSubmenu.forEach(i => i.classList.remove('active'));
            document.querySelectorAll('.submenu .menu-item').forEach(i => i.classList.remove('active'));
            menuItem.classList.add('active');

            const menuText = menuItem.querySelector('span').textContent.trim();

            setTimeout(() => {
                updateContent(hash);
                updateBreadcrumb(hash);
                addTag(menuText, hash);
            }, 0);

            return;
        }
    }

    // 如果没有有效的hash，激活首页标签
    Logger.log('No valid hash found, showing dashboard');
    const existingDashboardTag = document.querySelector('.tags-view-item[data-page="dashboard"]');
    if (existingDashboardTag) {
        activateTag(existingDashboardTag);
    }
    updateContent('dashboard');
    updateBreadcrumb('dashboard');
}

function handleHashChange(e) {
    // 如果是程序触发的 hash 变更，忽略一次
    if (ignoreNextHashChange) {
        ignoreNextHashChange = false;
        return;
    }
    Logger.log('Hash changed:', window.location.hash);
    const hash = window.location.hash.slice(1);

    if (hash) {
        const menuItem = document.querySelector(`.menu-item[data-page="${hash}"]`);
        if (menuItem) {
            const menuText = menuItem.querySelector('span').textContent.trim();
            updateContent(hash);
            updateBreadcrumb(hash);
            addTag(menuText, hash);
        } else {
            Logger.log('Invalid hash, reverting to previous state');
            history.back();
        }
    }
}

function getPageTitle(page) {
    switch(page) {
        case 'marking-record':
            return '打印记录';
        case 'dashboard':
            return '首页';
        case 'cpu-controller':
            return 'CPU_控制器';
        case 'cpu-controller-vue':
            return 'CPU_控制器v';
        case 'io-module':
            return 'IO模块';
        case 'io-module-vue':
            return 'IO模块v';
        case 'coupler':
            return '耦合器';
        case 'coupler-vue':
            return '耦合器V';
        case 'board-test':
            return '板测试';
        case 'fault-entry':
            return '故障品录入';
        case 'fault-code':
            return '故障码';
        case 'document-center':
            return '文档中心';
        case 'barcode-comparison':
            return '条码比对';
        case 'barcode-binding':
            return '扫码绑定';
        case 'batch-query':
            return '产品批次查询';
        case 'data-statistics':
            return '据统计';
        case 'about':
            return '关于';
        case 'fault-query':
            return '故障品查询';
        case 'product-test-query':
            return '成品测试查询';
        case 'product-management':
            return '产品管理';
        case 'order-management':
            return '工单管理';
        case 'shipment-barcode':
            return '出货条码录入';
        case 'shipment-query':
            return '出货查询';
        case 'quality-inspection':
            return '质检记录';
        case 'self-inspection':
            return '检验过程';
        case 'inspection-record':
            return '检验记录';
        case 'after-sales-query':
            return '售后查询';
        case 'sn-print-record':
            return 'SN打印';
        case 'laser-marking':
            return '激光打标';
        case 'firmware-management':
            return '固件管理';
        case 'all-firmware':
            return '所有固件';
        case 'pending-firmware':
            return '待审核固件';
        case 'obsolete-firmware':
            return '作废版本';
        case 'usage-record':
            return '使用记录';
        case 'version-comparison':
            return '版本比对';
        default:
            return capitalize(page);
    }
}

function loadCouplerPage() {
    content.innerHTML = '<div id="coupler-content"></div>';
    loadCSS('../static/page_js_css/Coupler.css');
    loadScript('../static/page_js_css/Coupler.js', () => {
        initCouplerPage();
    });
}

function adjustSidebar() {
    if (window.innerWidth <= 768) {
        sidebar.classList.add('collapsed');
        mainContainer.style.marginLeft = '64px';
    } else {
        sidebar.classList.remove('collapsed');
        mainContainer.style.marginLeft = 'var(--sidebar-width)';
    }
    adjustFixedHeader();
}

window.addEventListener('resize', () => {
    adjustSidebar();
});

function adjustFixedHeader() {
    const fixedHeader = document.querySelector('.fixed-header');
    if (sidebar.classList.contains('collapsed')) {
        fixedHeader.style.left = '64px';
    } else {
        fixedHeader.style.left = 'var(--sidebar-width)';
    }
}

function switchPage(pageName) {
    window.dispatchEvent(new CustomEvent('pageChange'));

    switch(pageName) {
        case 'document-center':
            initDocumentCenter();
            break;
    }
}

// 在现有代码中添加登出处理
function handleLogout() {
    document.cookie = 'token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
    const userSpan = document.getElementById('current-user');
    if (userSpan) {
        userSpan.textContent = 'Vue Admin';
    }
    window.location.href = '/login';
}

// 在用户下拉菜单的事件监听中添加登出处理
document.querySelector('a[href="#logout"]').addEventListener('click', (e) => {
    e.preventDefault();
    handleLogout();
});

// 添加获取用户信息的函数
async function fetchUserInfo() {
    try {
        const response = await fetch('/api/user/info');
        const data = await response.json();

        if (data.success) {
            const userSpan = document.getElementById('current-user');
            if (userSpan) {
                userSpan.textContent = data.username;
            }
        } else {
            Logger.error('Failed to get user info:', data.message);
        }
    } catch (error) {
        Logger.error('Error fetching user info:', error);
    }
}

// 添加个人信息相关的代码用户修改密码
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('profile-modal');
    const profileLink = document.getElementById('profile-link');
    const closeBtn = modal.querySelector('.close');
    const cancelBtn = modal.querySelector('.cancel');
    const form = document.getElementById('profile-form');

    // 打开模态框
    profileLink.addEventListener('click', function(e) {
        e.preventDefault();
        modal.style.display = 'block';
    });

    // 关闭模态框
    function closeModal() {
        modal.style.display = 'none';
        form.reset();
    }

    closeBtn.addEventListener('click', closeModal);
    cancelBtn.addEventListener('click', closeModal);

    // 点击模态框外部关闭
    window.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });

    // 处理表单提交
    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        const oldPassword = document.getElementById('old-password').value;
        const newPassword = document.getElementById('new-password').value;
        const confirmPassword = document.getElementById('confirm-password').value;

        // 验证表单
        if (!oldPassword) {
            alert('请输入原密码');
            return;
        }

        if (!newPassword) {
            alert('请输入新密码');
            return;
        }

        if (newPassword !== confirmPassword) {
            Swal.fire({
                title: '错误',
                text: '两次输入的新密码不一致',
                icon: 'error'
            });
            return;
        }

        try {
            const response = await fetch('/api/user/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    password: newPassword,
                    oldPassword: oldPassword
                })
            });

            const data = await response.json();

            if (data.success) {
                Swal.fire({
                    title: data.title,
                    text: data.message,
                    icon: data.icon
                }).then(() => {
                    closeModal();
                });
            } else {
                Swal.fire({
                    title: data.title,
                    text: data.message,
                    icon: data.icon
                });
            }
        } catch (error) {
            Logger.error('Error updating password:', error);
            Swal.fire({
                title: '错误',
                text: '修改失败，请稍后重试',
                icon: 'error'
            });
        }
    });
});
//添加的用户信息修改密码结束

// ===== Vue固件管理页面加载函数 =====
// 全局状态跟踪，避免重复加载Vue库
window.loadedVueLibraries = {
    vue: false,
    elementPlus: false,
    elementPlusIcons: false,
    elementPlusCss: false
};

// Vue IO模块页面加载函数
function loadVueIOModulePage(pageName, containerId) {
    Logger.log(`Loading Vue IO Module page: ${pageName}`);
    
    // 清理之前的Vue应用
    if (window.currentIOModuleVueApp && typeof window.currentIOModuleVueApp.unmount === 'function') {
        Logger.log("Unmounting previous IO Module Vue app");
        window.currentIOModuleVueApp.unmount();
        window.currentIOModuleVueApp = null;
    }
    
    const basePath = '/static/page_js_css/';
    
    // 依赖资源路径 - 现代化分层样式架构
    const dependencies = {
        vueCdn: '/static/lib/vue/vue.global.js',
        elementPlusCdn: '/static/lib/element-plus/index.full.js',
        elementPlusIconsCdn: '/static/lib/element-plus-icons/index.iife.js',
        elementPlusCss: '/static/lib/element-plus/index.css',
        lucideCdn: '/static/lib/lucide/lucide.min.js',
        pageCss: basePath + 'IOModuleVue.css',           // IO模块专用样式
        pageApp: basePath + 'IOModuleVue.js'
    };
    
    // 链式加载资源
    loadVueIOModuleLibrariesSequentially(dependencies, pageName, containerId);
}

// Vue 耦合器页面加载函数
function loadVueCouplerPage(pageName, containerId) {
    Logger.log(`Loading Vue Coupler page: ${pageName}`);
    
    // 清理之前的Vue应用
    if (window.currentCouplerVueApp && typeof window.currentCouplerVueApp.unmount === 'function') {
        Logger.log("Unmounting previous Coupler Vue app");
        window.currentCouplerVueApp.unmount();
        window.currentCouplerVueApp = null;
    }
    
    const basePath = '/static/page_js_css/';
    
    // 依赖资源路径（使用本地库文件）
    const dependencies = {
        vueCdn: '/static/lib/vue/vue.global.js',
        elementPlusCdn: '/static/lib/element-plus/index.full.js',
        elementPlusIconsCdn: '/static/lib/element-plus-icons/index.iife.js',
        elementPlusCss: '/static/lib/element-plus/index.css',
        pageCss: basePath + 'CouplerVue.css',
        pageApp: basePath + 'CouplerVue.js'
    };
    
    // 链式加载资源
    loadVueCouplerLibrariesSequentially(dependencies, pageName, containerId);
}

function loadVueIOModuleLibrariesSequentially(deps, pageName, containerId) {
    // 加载CSS - 确保正确的加载顺序和强制刷新
    loadCSSIfNotLoaded(deps.elementPlusCss, 'element-plus-css');
    
    // 移除已存在的样式文件，强制重新加载
    const existingPageCSS = document.querySelector('link[href*="IOModuleVue.css"]');
    if (existingPageCSS) {
        existingPageCSS.remove();
    }
    
    // 强制重新加载样式文件，添加时间戳防止缓存
    loadCSS(deps.pageCss + '?v=' + Date.now());          // 强制刷新IO模块专用样式
    
    // 链式加载JS
    loadScriptIfNotLoaded(deps.vueCdn, 'vue-lib', () => {
        Logger.log('Vue 3 loaded');
        window.loadedVueLibraries.vue = true;
        
        loadScriptIfNotLoaded(deps.elementPlusCdn, 'element-plus-lib', () => {
            Logger.log('Element Plus loaded');
            window.loadedVueLibraries.elementPlus = true;
            
            loadScriptIfNotLoaded(deps.elementPlusIconsCdn, 'element-plus-icons-lib', () => {
                Logger.log('Element Plus Icons loaded');
                window.loadedVueLibraries.elementPlusIcons = true;
                
                // 加载Lucide图标库
                loadScriptIfNotLoaded(deps.lucideCdn, 'lucide-icons-lib', () => {
                    Logger.log('Lucide Icons loaded');
                    
                    // 直接加载页面应用
                    loadScript(deps.pageApp, () => {
                        Logger.log(`${pageName} app loaded and mounted to #${containerId}`);
                        
                        // 初始化Lucide图标
                        setTimeout(() => {
                            if (window.lucide && window.lucide.createIcons) {
                                window.lucide.createIcons();
                            }
                        }, 100);
                    });
                });
            });
        });
    });
}

function loadVueCouplerLibrariesSequentially(deps, pageName, containerId) {
    // 加载CSS
    loadCSSIfNotLoaded(deps.elementPlusCss, 'element-plus-css');
    loadCSS(deps.pageCss);
    
    // 链式加载JS
    loadScriptIfNotLoaded(deps.vueCdn, 'vue-lib', () => {
        Logger.log('Vue 3 loaded');
        window.loadedVueLibraries.vue = true;
        
        loadScriptIfNotLoaded(deps.elementPlusCdn, 'element-plus-lib', () => {
            Logger.log('Element Plus loaded');
            window.loadedVueLibraries.elementPlus = true;
            
            loadScriptIfNotLoaded(deps.elementPlusIconsCdn, 'element-plus-icons-lib', () => {
                Logger.log('Element Plus Icons loaded');
                window.loadedVueLibraries.elementPlusIcons = true;
                
                // 直接加载页面应用
                loadScript(deps.pageApp, () => {
                    Logger.log(`${pageName} app loaded and mounted to #${containerId}`);
                });
            });
        });
    });
}

function loadVueFirmwarePage(pageName, containerId) {
    Logger.log(`Loading Vue firmware page: ${pageName}`);
    
    // 清理之前的Vue应用
    if (window.currentFirmwareApp && typeof window.currentFirmwareApp.unmount === 'function') {
        Logger.log("Unmounting previous firmware Vue app");
        window.currentFirmwareApp.unmount();
        window.currentFirmwareApp = null;
    }
    
    const basePath = '/static/page_js_css/firmware/';
    
    // 依赖资源路径（扁平化结构）- 使用本地库文件
    const dependencies = {
        vueCdn: '/static/lib/vue/vue.global.js',
        elementPlusCdn: '/static/lib/element-plus/index.full.js',
        elementPlusIconsCdn: '/static/lib/element-plus-icons/index.iife.js',
        elementPlusCss: '/static/lib/element-plus/index.css',
        commonCss: basePath + 'firmware-common.css',
        commonPagingCss: basePath + 'firmware-common-paging.css',  // 添加共享分页样式
        firmwareDataManager: '/static/js/utils/FirmwareDataManager.js',  // 添加数据管理器
        firmwareUtils: basePath + 'firmware-utils.js',  // 可选，包含工具函数和API
        commonPagingJs: basePath + 'firmware-common-paging.js',  // 添加共享分页功能
        pageCss: basePath + pageName + '.css',
        pageApp: basePath + pageName + '.js'
    };
    
    // 链式加载资源
    loadVueLibrariesSequentially(dependencies, pageName, containerId);
}

function loadVueLibrariesSequentially(deps, pageName, containerId) {
    // 加载CSS
    loadCSSIfNotLoaded(deps.elementPlusCss, 'element-plus-css');
    loadCSS(deps.commonCss);
    loadCSS(deps.commonPagingCss);  // 加载共享分页样式
    loadCSS(deps.pageCss);
    
    // 链式加载JS
    loadScriptIfNotLoaded(deps.vueCdn, 'vue-lib', () => {
        Logger.log('Vue 3 loaded');
        window.loadedVueLibraries.vue = true;
        
        loadScriptIfNotLoaded(deps.elementPlusCdn, 'element-plus-lib', () => {
            Logger.log('Element Plus loaded');
            window.loadedVueLibraries.elementPlus = true;
            
            loadScriptIfNotLoaded(deps.elementPlusIconsCdn, 'element-plus-icons-lib', () => {
                Logger.log('Element Plus Icons loaded');
                window.loadedVueLibraries.elementPlusIcons = true;
                
                // 优先加载固件数据管理器
                loadScriptIfNotLoaded(deps.firmwareDataManager, 'firmware-data-manager', () => {
                    Logger.log('Firmware Data Manager loaded');
                    
                    // 加载共享分页功能
                    loadScriptIfNotLoaded(deps.commonPagingJs, 'firmware-common-paging', () => {
                        Logger.log('Firmware Common Paging loaded');
                        
                        // 可选加载共享工具（如果存在）
                        if (deps.firmwareUtils) {
                            loadScript(deps.firmwareUtils, () => {
                                Logger.log('Firmware utils loaded');
                                loadScript(deps.pageApp, () => {
                                    Logger.log(`${pageName} app loaded and mounted to #${containerId}`);
                                });
                            });
                        } else {
                            // 直接加载页面应用
                            loadScript(deps.pageApp, () => {
                                Logger.log(`${pageName} app loaded and mounted to #${containerId}`);
                            });
                        }
                    });
                });
            });
        });
    });
}

// 工具函数：避免重复加载JS
function loadScriptIfNotLoaded(url, id, callback) {
    if (document.getElementById(id)) {
        if (callback) callback();
        return;
    }
    
    const script = document.createElement('script');
    script.id = id;
    script.src = url;
    script.onload = callback;
    script.onerror = () => Logger.error(`Failed to load script: ${url}`);
    document.head.appendChild(script);
}

// 工具函数：避免重复加载CSS
function loadCSSIfNotLoaded(url, id) {
    if (document.getElementById(id)) return;
    
    const link = document.createElement('link');
    link.id = id;
    link.rel = 'stylesheet';
    link.href = url;
    link.onerror = () => Logger.error(`Failed to load CSS: ${url}`);
    document.head.appendChild(link);
}

function loadVueCPUControllerPage(pageName, containerId) {
    Logger.log(`Loading Vue CPU Controller page: ${pageName}`);
    
    // 清理之前的Vue应用
    if (window.currentCPUControllerVueApp && typeof window.currentCPUControllerVueApp.unmount === 'function') {
        Logger.log("Unmounting previous CPU Controller Vue app");
        window.currentCPUControllerVueApp.unmount();
        window.currentCPUControllerVueApp = null;
    }
    
    // 清空容器内容
    const container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = '';
    }
    
    const basePath = '/static/page_js_css/';
    
    // 依赖资源路径 - 现代化分层样式架构
    const dependencies = {
        vueCdn: '/static/lib/vue/vue.global.js',
        elementPlusCdn: '/static/lib/element-plus/index.full.js',
        elementPlusIconsCdn: '/static/lib/element-plus-icons/index.iife.js',
        elementPlusCss: '/static/lib/element-plus/index.css',
        sharedCss: basePath + 'shared-styles.css',           // 公共样式
        cpuSpecificCss: basePath + 'CPUControllerVue.css',   // CPU控制器Vue专用样式
        pageCss: basePath + 'cpu-controller-styles.css',     // CPU控制器额外样式
        pageApp: basePath + 'CPUControllerVue.js?v=' + Date.now()
    };
    
    // 链式加载资源
    loadVueCPUControllerLibrariesSequentially(dependencies, pageName, containerId);
}

function loadVueCPUControllerLibrariesSequentially(deps, pageName, containerId) {
    // 加载CSS - 确保正确的加载顺序和强制刷新
    loadCSSIfNotLoaded(deps.elementPlusCss, 'element-plus-css');
    
    // 移除已存在的样式文件，强制重新加载
    const existingSharedCSS = document.querySelector('link[href*="shared-styles.css"]');
    if (existingSharedCSS) {
        existingSharedCSS.remove();
    }
    const existingCPUCSS = document.querySelector('link[href*="CPUControllerVue.css"]');
    if (existingCPUCSS) {
        existingCPUCSS.remove();
    }
    const existingPageCSS = document.querySelector('link[href*="cpu-controller-styles.css"]');
    if (existingPageCSS) {
        existingPageCSS.remove();
    }
    
    // 强制重新加载样式文件，添加时间戳防止缓存 - 正确的加载顺序
    loadCSS(deps.sharedCss + '?v=' + Date.now());        // 强制刷新公共样式
    loadCSS(deps.cpuSpecificCss + '?v=' + Date.now());   // 强制刷新CPU专用样式
    loadCSS(deps.pageCss + '?v=' + Date.now());          // 强制刷新额外样式
    
    // 链式加载JS
    loadScriptIfNotLoaded(deps.vueCdn, 'vue-lib', () => {
        Logger.log('Vue 3 loaded');
        window.loadedVueLibraries.vue = true;
        
        loadScriptIfNotLoaded(deps.elementPlusCdn, 'element-plus-lib', () => {
            Logger.log('Element Plus loaded');
            window.loadedVueLibraries.elementPlus = true;
            
            loadScriptIfNotLoaded(deps.elementPlusIconsCdn, 'element-plus-icons-lib', () => {
                Logger.log('Element Plus Icons loaded');
                window.loadedVueLibraries.elementPlusIcons = true;
                
                // 移除之前加载的CPUControllerVue脚本（如果存在）
                const existingScript = document.querySelector('script[src*="CPUControllerVue.js"]');
                if (existingScript) {
                    existingScript.remove();
                }
                
                // 加载页面应用
                loadScript(deps.pageApp, () => {
                    Logger.log(`${pageName} app loaded and mounted to #${containerId}`);
                });
            });
        });
    });
}
