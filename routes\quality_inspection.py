import sys
import os
# 将项目根目录添加到 Python 路径中
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
# 然后再导入模型
from flask import Blueprint, request, jsonify, current_app
from werkzeug.utils import secure_filename
from datetime import datetime
from database.db_manager import DatabaseManager
from models.modelquality_inspection import ProductType, QualityInspectionWorkOrder, InspectionItem, InspectionAttachment, Product, ProductInspectionStatus
import logging
from sqlalchemy import func
from enum import Enum

quality_inspection_bp = Blueprint('quality_inspection', __name__)
logger = logging.getLogger(__name__)

# 获取产品类型列表
@quality_inspection_bp.route('/product-types', methods=['GET'])
def get_product_types():
    try:
        db = DatabaseManager()
        with db.get_session() as session:
            types = session.query(ProductType).all()
            return jsonify({
                'success': True,
                'data': [{
                    'id': t.id,
                    'type_code': t.type_code,
                    'type_name': t.type_name
                } for t in types]
            })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# 根据产品类型获取检验项目
@quality_inspection_bp.route('/inspection-items', methods=['GET'])
def get_inspection_items():
    product_type_id = request.args.get('product_type_id')
    if not product_type_id:
        return jsonify({'success': False, 'message': '缺少产品类型ID'}), 400

    try:
        db = DatabaseManager()
        with db.get_session() as session:
            items = session.query(InspectionItem)\
                .filter_by(product_type_id=product_type_id)\
                .order_by(InspectionItem.stage, InspectionItem.display_order)\
                .all()

            # 按阶段分组
            grouped_items = {}
            for item in items:
                if item.stage not in grouped_items:
                    grouped_items[item.stage] = []
                grouped_items[item.stage].append({
                    'id': item.id,
                    'item_no': item.item_no,
                    'item_name': item.item_name,
                    'is_required': item.is_required
                })

            return jsonify({
                'success': True,
                'data': grouped_items
            })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# 获取工单信息
@quality_inspection_bp.route('/work-order/<path:work_order_no>', methods=['GET'])
def get_work_order(work_order_no):
    try:
        db = DatabaseManager()
        with db.get_session() as session:
            work_order = session.query(QualityInspectionWorkOrder)\
                .filter_by(work_order_no=work_order_no)\
                .first()

            if not work_order:
                return jsonify({
                    'success': False,
                    'message': '工单不存在'
                }), 404

            # 获取检验状态记录
            inspection_statuses = session.query(ProductInspectionStatus)\
                .filter_by(work_order_id=work_order.id)\
                .all()

            # 获取附件
            attachments = session.query(InspectionAttachment)\
                .filter_by(work_order_id=work_order.id)\
                .all()

            # 获取所有产品
            products = session.query(Product)\
                .filter_by(work_order_id=work_order.id)\
                .all()

            # 将工单信息转换为字典
            work_order_data = {
                'id': work_order.id,
                'work_order_no': work_order.work_order_no,
                'product_type_id': work_order.product_type_id,
                'product_type_name': work_order.product_type.type_name,
                'product_code': work_order.product_type.type_code,
                'status': work_order.status,
                'created_at': work_order.created_at.strftime('%Y-%m-%d %H:%M:%S') if work_order.created_at else None,
                'is_rework': work_order.is_rework
            }

            # Query ProductInspectionStatus, ensuring correct enum handling for stage and role
            all_statuses_for_work_order = session.query(ProductInspectionStatus).filter(
                ProductInspectionStatus.work_order_id == work_order.id
            ).order_by(ProductInspectionStatus.inspection_time).all()

            # Aggregate statuses: one entry per (stage, inspector_role)
            # is_final is true if ANY record for that group was 'final'
            # Other details (inspector, inspection_time) are from the LATEST record for that group.
            
            aggregated_statuses_map = {}

            for status_rec in all_statuses_for_work_order:
                stage_val = status_rec.stage.value if isinstance(status_rec.stage, Enum) else status_rec.stage
                role_val = status_rec.inspector_role.value if isinstance(status_rec.inspector_role, Enum) else status_rec.inspector_role
                current_key = (stage_val, role_val)

                if current_key not in aggregated_statuses_map:
                    aggregated_statuses_map[current_key] = {
                        "stage": stage_val,
                        "inspector_role": role_val,
                        "inspector": status_rec.inspector,
                        "inspection_time": status_rec.inspection_time, # Will be updated to latest
                        "is_final": False, # Initialize
                        # Store submission_type from the latest record for potential debugging or other uses, though processSubmissions doesn't directly use it.
                        "latest_submission_type": status_rec.submission_type.value if isinstance(status_rec.submission_type, Enum) else status_rec.submission_type
                    }
                
                # Update with latest info
                if status_rec.inspection_time >= aggregated_statuses_map[current_key]["inspection_time"]:
                    aggregated_statuses_map[current_key]["inspector"] = status_rec.inspector
                    aggregated_statuses_map[current_key]["inspection_time"] = status_rec.inspection_time
                    aggregated_statuses_map[current_key]["latest_submission_type"] = status_rec.submission_type.value if isinstance(status_rec.submission_type, Enum) else status_rec.submission_type

                if status_rec.submission_type == 'final':
                    aggregated_statuses_map[current_key]["is_final"] = True
            
            # Convert map to list and format inspection_time
            aggregated_inspection_statuses = []
            for data in aggregated_statuses_map.values():
                data["inspection_time"] = data["inspection_time"].isoformat() if data["inspection_time"] else None
                aggregated_inspection_statuses.append(data)

            # 构建附件记录
            attachment_records = []
            for attachment in attachments:
                attachment_records.append({
                    'id': attachment.id,
                    'stage': attachment.stage,
                    'inspector_role': attachment.inspector_role,
                    'file_name': attachment.file_name,
                    'file_path': attachment.file_path,
                    'file_type': attachment.file_type,
                    'file_size': attachment.file_size,
                    'uploaded_by': attachment.uploaded_by,
                    'upload_time': attachment.upload_time.strftime('%Y-%m-%d %H:%M:%S') if attachment.upload_time else None
                })

            # 构建产品记录
            product_records = []
            for product in products:
                product_records.append({
                    'id': product.id,
                    'serial_no': product.serial_no,
                    'created_at': product.created_at.strftime('%Y-%m-%d %H:%M:%S') if product.created_at else None
                })

            response_data = {
                'work_order': work_order_data,
                'inspection_statuses': aggregated_inspection_statuses,
                'products': product_records
            }

            # Ensure attachment stage and inspector_role are string values
            processed_attachment_records = []
            for record in attachment_records:
                if 'stage' in record and isinstance(record['stage'], Enum):
                    record['stage'] = record['stage'].value
                if 'inspector_role' in record and isinstance(record['inspector_role'], Enum):
                    record['inspector_role'] = record['inspector_role'].value
                processed_attachment_records.append(record)
            response_data['attachments'] = processed_attachment_records

            return jsonify({'success': True, 'data': response_data})

    except Exception as e:
        print(f"Error getting work order: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取工单信息时发生错误: {str(e)}'
        }), 500

# 创建工单
@quality_inspection_bp.route('/work-order', methods=['POST'])
def create_work_order():
    data = request.get_json()
    work_order_no = data.get('work_order_no')
    product_type_id = data.get('product_type_id')
    is_rework = data.get('is_rework', False)  # 新增参数，标识是否为返工工单

    if not work_order_no or not product_type_id:
        return jsonify({
            'success': False,
            'message': '工单号和产品类型ID不能为空'
        }), 400

    try:
        db = DatabaseManager()
        with db.get_session() as session:
            # 检查工单是否已存在
            work_order = session.query(QualityInspectionWorkOrder)\
                .filter_by(work_order_no=work_order_no)\
                .first()

            if work_order:
                if work_order.product_type_id != int(product_type_id):
                    return jsonify({
                        'success': False,
                        'message': '工单已存在且产品类型不匹配'
                    }), 400
            else:
                # 创建新工单
                work_order = QualityInspectionWorkOrder(
                    work_order_no=work_order_no,
                    product_type_id=product_type_id,
                    is_rework=is_rework  # 设置是否为返工工单
                )
                session.add(work_order)
                session.commit()

            return jsonify({
                'success': True,
                'data': {
                    'id': work_order.id,
                    'work_order_no': work_order.work_order_no,
                    'product_type_id': work_order.product_type_id,
                    'status': work_order.status,
                    'is_rework': work_order.is_rework
                }
            })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# 保存检验记录
@quality_inspection_bp.route('/records', methods=['POST'])
def save_inspection_records():
    data = request.get_json()
    records_payload = data.get('records', [])
    product_ids = data.get('product_ids', [])
    is_final_submission = data.get('is_final_submission', False)  # 标识是否为最终提交

    if not records_payload:
        return jsonify({'success': False, 'message': '没有要保存的记录'}), 400

    if not product_ids:
        return jsonify({'success': False, 'message': '没有指定产品ID'}), 400

    # 从第一条记录获取必要信息 (假设同一批提交属于同工单同角色)
    if not records_payload[0]:
        return jsonify({'success': False, 'message': '记录数据无效'}), 400

    work_order_id = records_payload[0].get('work_order_id')
    inspector_role = records_payload[0].get('inspector_role')
    checked_by = records_payload[0].get('checked_by')
    inspection_item_id_for_stage = records_payload[0].get('inspection_item_id')

    if not all([work_order_id, inspector_role, checked_by, inspection_item_id_for_stage]):
        return jsonify({'success': False, 'message': '记录数据缺少必要字段'}), 400

    try:
        db = DatabaseManager()
        with db.get_session() as session:
            # 1. 获取阶段信息
            item_for_stage = session.query(InspectionItem).filter_by(id=inspection_item_id_for_stage).first()
            if not item_for_stage:
                return jsonify({'success': False, 'message': f'检验项目ID {inspection_item_id_for_stage} 无效'}), 400
            stage = item_for_stage.stage

            # 2. 检查是否已经提交过
            existing_status = session.query(ProductInspectionStatus).filter_by(
                work_order_id=work_order_id,
                stage=stage,
                inspector_role=inspector_role,
                submission_type='final'  # 检查是否有最终提交记录
            ).first()

            # 只有当已经存在最终提交记录并且现在也是最终提交时，才拒绝
            if existing_status and is_final_submission:
                logger.warning(f"Attempt to re-submit final: WO={work_order_id}, Stage={stage}, Role={inspector_role}")
                return jsonify({'success': False, 'message': f'阶段 {stage} 的 {inspector_role} 检验已最终提交，无法重复提交'}), 400

            # 3. 验证产品ID并检查是否已被其他人检验
            for product_id in product_ids:
                product = session.query(Product).filter_by(id=product_id).first()
                if not product or product.work_order_id != int(work_order_id):
                    return jsonify({'success': False, 'message': f'产品ID {product_id} 无效或不属于当前工单'}), 400

                # 检查产品是否已经被检验过
                if inspector_role == 'self':  # 只对自检角色进行此检查
                    existing_status = session.query(ProductInspectionStatus).filter_by(
                        product_id=product_id,
                        stage=stage,
                        inspector_role=inspector_role
                    ).first()

                    if existing_status:
                        return jsonify({'success': False, 'message': f'产品 {product.serial_no} 已被其他人检验完成'}), 400

            # 4. 保存检验状态记录
            logger.info(f"Saving inspection status for: WO={work_order_id}, Stage={stage}, Role={inspector_role}, Products={product_ids}, Final={is_final_submission}")

            # 确定提交类型
            submission_type = 'final' if is_final_submission else 'partial'

            # 为每个产品创建检验状态记录
            for product_id in product_ids:
                # 检查是否已存在记录
                existing_status = session.query(ProductInspectionStatus).filter_by(
                    product_id=product_id,
                    stage=stage,
                    inspector_role=inspector_role
                ).first()

                if existing_status:
                    # 更新现有记录
                    existing_status.is_passed = True
                    existing_status.inspector = checked_by
                    existing_status.inspection_time = datetime.now()
                    existing_status.submission_type = submission_type  # 更新提交类型
                else:
                    # 创建新记录
                    new_status = ProductInspectionStatus(
                        work_order_id=work_order_id,
                        product_id=product_id,
                        stage=stage,
                        inspector_role=inspector_role,
                        is_passed=True,
                        inspector=checked_by,
                        inspection_time=datetime.now(),
                        submission_type=submission_type  # 设置提交类型
                    )
                    session.add(new_status)

            # ***** 关键修改：在检查工单完成状态前，刷新会话以确保查询能看到最新的 ProductInspectionStatus 记录 *****
            session.flush()

            # 5. 更新工单状态
            if is_final_submission:
                work_order = session.query(QualityInspectionWorkOrder).filter_by(id=work_order_id).first()
                if work_order and work_order.status == 'pending':
                    work_order.status = 'processing'

                # 检查是否所有阶段都已完成
                check_work_order_completion(session, work_order_id)

            # 6. 提交事务
            session.commit()

            # 构建响应数据
            response_data = {
                'success': True,
                'is_final_submission': is_final_submission,
                'products_completed': len(product_ids)
            }

            # 如果是最终提交，添加产品统计信息
            if is_final_submission:
                total_products = session.query(Product).filter_by(work_order_id=work_order_id).count()
                completed_products = len(product_ids)
                response_data.update({
                    'total_products': total_products,
                    'completed_products': completed_products,
                    'partial_submission': completed_products < total_products
                })

            return jsonify(response_data)

    except Exception as e:
        session.rollback()
        logger.error(f"Error saving inspection records for WO={work_order_id}, Role={inspector_role}: {str(e)}", exc_info=True)
        error_message = f"保存记录时发生错误: {str(e)}"
        return jsonify({'success': False, 'message': error_message}), 500

# 检查工单是否所有阶段都已完成
def check_work_order_completion(session, work_order_id):
    """检查工单是否已完成
    修改后的逻辑是：
    1. 对于每个阶段-角色组合，至少要有一个产品(SN)完成'final'最终提交
    2. 不同阶段-角色组合检验的SN号可以不一致
    3. 当所有9个阶段-角色组合（3阶段 × 3角色）都至少有一个SN完成final提交时，工单状态就设为completed
    """
    work_order = session.query(QualityInspectionWorkOrder).get(work_order_id)
    if not work_order or not work_order.products:
        return
    
    # 定义所有必需的阶段和角色
    required_stages = ['assembly', 'test', 'packaging']
    required_roles = ['first', 'self', 'ipqc']
    
    # 用于跟踪各阶段的完成状态
    stages = {
        'assembly': {'completed': False, 'field': 'assembly_stage_completed'},
        'test': {'completed': False, 'field': 'test_stage_completed'},
        'packaging': {'completed': False, 'field': 'packaging_stage_completed'}
    }
    
    # 查询所有最终提交记录，获取每个阶段-角色组合的完成情况
    final_submissions = session.query(
        ProductInspectionStatus.stage,
        ProductInspectionStatus.inspector_role,
        ProductInspectionStatus.product_id
    ).filter(
        ProductInspectionStatus.work_order_id == work_order_id,
        ProductInspectionStatus.submission_type == 'final'
    ).all()
    
    # 统计每个阶段-角色组合是否有final提交记录
    completed_combinations = set()
    for submission in final_submissions:
        stage = submission.stage
        role = submission.inspector_role
        # 将阶段-角色组合添加到已完成集合
        completed_combinations.add((stage, role))
    
    # 检查每个阶段是否所有角色都有最终提交记录
    for stage in required_stages:
        stage_roles_completed = set()
        for role in required_roles:
            if (stage, role) in completed_combinations:
                stage_roles_completed.add(role)
        
        # 如果该阶段的所有角色都有最终提交记录，标记该阶段为完成
        if len(stage_roles_completed) == len(required_roles):
            stages[stage]['completed'] = True
    
    # 检查所有9个阶段-角色组合是否都有完成记录
    total_required_combinations = len(required_stages) * len(required_roles)  # 应该是9个
    all_combinations_completed = len(completed_combinations) >= total_required_combinations
    
    # 验证是否确实覆盖了所有必需的组合
    all_required_combinations = set()
    for stage in required_stages:
        for role in required_roles:
            all_required_combinations.add((stage, role))
    
    # 检查所有必需的组合是否都已完成
    all_combinations_completed = all_required_combinations.issubset(completed_combinations)
    
    # 更新工单状态和各阶段状态
    status_changed = False
    
    # 更新各个阶段的完成状态
    for stage, info in stages.items():
        current_value = getattr(work_order, info['field'])
        if info['completed'] and not current_value:
            setattr(work_order, info['field'], True)
            status_changed = True
            logger.info(f"工单 {work_order_id} 阶段 '{stage}' 标记为完成")
        elif not info['completed'] and current_value:
            # 如果之前标记为完成但现在不满足条件，重置为未完成
            setattr(work_order, info['field'], False)
            status_changed = True
            logger.info(f"工单 {work_order_id} 阶段 '{stage}' 重置为未完成")
    
    # 根据所有组合的完成情况更新工单状态
    if all_combinations_completed:
        if work_order.status != 'completed':
            work_order.status = 'completed'
            status_changed = True
            logger.info(f"工单 {work_order_id} 标记为已完成，所有9个阶段-角色组合都已完成")
    elif len(completed_combinations) > 0:
        # 如果有部分完成但未全部完成，设为processing
        if work_order.status not in ['processing', 'completed']:
            work_order.status = 'processing'
            status_changed = True
            logger.info(f"工单 {work_order_id} 标记为处理中，已完成 {len(completed_combinations)}/9 个组合")
    
    # 如果有任何状态变化，提交更新
    if status_changed:
        try:
            session.commit()
            logger.info(f"工单 {work_order_id} 状态更新成功")
        except Exception as e:
            session.rollback()
            logger.error(f"工单 {work_order_id} 状态更新失败: {str(e)}")
            raise

# 上传附件
@quality_inspection_bp.route('/attachments', methods=['POST'])
def upload_attachment():
    if 'file' not in request.files:
        return jsonify({'success': False, 'message': '没有文件'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'success': False, 'message': '没有选择文件'}), 400

    work_order_id = request.form.get('work_order_id')
    stage = request.form.get('stage')
    inspector_role = request.form.get('inspector_role')
    uploaded_by = request.form.get('uploaded_by')

    if not all([work_order_id, stage, inspector_role, uploaded_by]):
        return jsonify({'success': False, 'message': '缺少必要参数'}), 400

    try:
        # 确保上传目录存在
        upload_dir = '/var/data/attachments'  # 修改后的存储路径
        os.makedirs(upload_dir, exist_ok=True)

        # 安全地保存文件
        filename = secure_filename(file.filename)
        file_path = os.path.join(upload_dir, filename)
        file.save(file_path)

        db = DatabaseManager()
        with db.get_session() as session:
            attachment = InspectionAttachment(
                work_order_id=work_order_id,
                stage=stage,
                inspector_role=inspector_role,
                file_name=filename,
                file_path=file_path,
                file_type=os.path.splitext(filename)[1],
                file_size=os.path.getsize(file_path),
                uploaded_by=uploaded_by
            )
            session.add(attachment)
            session.commit()

            return jsonify({
                'success': True,
                'data': {
                    'id': attachment.id,
                    'file_name': attachment.file_name,
                    'file_type': attachment.file_type,
                    'file_size': attachment.file_size
                }
            })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# 删除附件
@quality_inspection_bp.route('/attachments/<int:attachment_id>', methods=['DELETE'])
def delete_attachment(attachment_id):
    try:
        db = DatabaseManager()
        with db.get_session() as session:
            attachment = session.query(InspectionAttachment)\
                .filter_by(id=attachment_id)\
                .first()

            if not attachment:
                return jsonify({
                    'success': False,
                    'message': '附件不存在'
                }), 404

            # 删除文件
            if os.path.exists(attachment.file_path):
                os.remove(attachment.file_path)

            # 删除记录
            session.delete(attachment)
            session.commit()

            return jsonify({'success': True})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# 获取附件文件
@quality_inspection_bp.route('/attachments/<int:attachment_id>/file', methods=['GET'])
def get_attachment_file(attachment_id):
    try:
        db = DatabaseManager()
        with db.get_session() as session:
            attachment = session.query(InspectionAttachment)\
                .filter_by(id=attachment_id)\
                .first()

            if not attachment:
                return jsonify({
                    'success': False,
                    'message': '附件不存在'
                }), 404

            # 检查文件是否存在
            if not os.path.exists(attachment.file_path):
                return jsonify({
                    'success': False,
                    'message': '文件不存在'
                }), 404

            # 导入 send_file 函数
            from flask import send_file

            # 返回文件
            return send_file(attachment.file_path, as_attachment=False)
    except Exception as e:
        logger.error(f"Error getting attachment file {attachment_id}: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': str(e)}), 500

# 检查SN号是否存在于工单中
@quality_inspection_bp.route('/check-serial-number', methods=['GET'])
def check_serial_number():
    try:
        work_order_id = request.args.get('work_order_id')
        serial_no = request.args.get('serial_no')

        if not work_order_id or not serial_no:
            return jsonify({'success': False, 'message': '工单ID和SN号不能为空'}), 400

        db = DatabaseManager()
        with db.get_session() as session:
            # 检查工单是否是返工工单
            work_order = session.query(QualityInspectionWorkOrder).filter_by(id=work_order_id).first()
            if not work_order:
                return jsonify({'success': False, 'message': '工单不存在'}), 404

            is_rework_work_order = work_order.is_rework

            # 查询产品是否存在于当前工单
            product = session.query(Product).filter_by(
                work_order_id=work_order_id,
                serial_no=serial_no
            ).first()

            if product:
                return jsonify({
                    'success': True,
                    'exists': True,
                    'product_id': product.id,
                    'is_rework': product.is_rework
                })

            # 如果是返工工单，检查SN号是否存在于其他工单
            if is_rework_work_order:
                original_product = session.query(Product).filter_by(serial_no=serial_no).first()
                if original_product:
                    return jsonify({
                        'success': True,
                        'exists': False,
                        'exists_in_other_work_order': True,
                        'original_product_id': original_product.id,
                        'original_work_order_id': original_product.work_order_id
                    })

            return jsonify({
                'success': True,
                'exists': False,
                'exists_in_other_work_order': False
            })
    except Exception as e:
        logger.error(f"Error checking serial number: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': str(e)}), 500

# 添加产品到工单
@quality_inspection_bp.route('/work-order/<int:work_order_id>/products', methods=['POST'])
def add_product(work_order_id):
    try:
        data = request.get_json()
        serial_no = data.get('serial_no')
        is_rework = data.get('is_rework', False)  # 从前端获取是否为返工产品

        if not serial_no:
            return jsonify({'success': False, 'message': 'SN号不能为空'}), 400

        db = DatabaseManager()
        with db.get_session() as session:
            # 检查工单是否存在
            work_order = session.query(QualityInspectionWorkOrder).filter_by(id=work_order_id).first()
            if not work_order:
                return jsonify({'success': False, 'message': '工单不存在'}), 404

            # 检查SN号是否已存在于当前工单
            existing_in_current = session.query(Product).filter_by(
                work_order_id=work_order_id,
                serial_no=serial_no
            ).first()

            if existing_in_current:
                return jsonify({'success': False, 'message': 'SN号已存在于当前工单'}), 400

            # 如果是普通工单（非返工工单），检查SN号是否已存在于其他工单
            if not is_rework and not work_order.is_rework:
                existing = session.query(Product).filter_by(serial_no=serial_no).first()
                if existing:
                    return jsonify({'success': False, 'message': 'SN号已存在于其他工单，普通工单不能添加已存在的SN号'}), 400

            # 确定是否为返工产品
            # 如果前端传递了is_rework参数，优先使用前端传递的值
            # 否则使用工单的is_rework值
            final_is_rework = is_rework or work_order.is_rework

            # 创建新产品
            product = Product(
                work_order_id=work_order_id,
                serial_no=serial_no,
                is_rework=final_is_rework  # 设置是否为返工产品
            )

            session.add(product)
            session.commit()

            return jsonify({
                'success': True,
                'product': {
                    'id': product.id,
                    'serial_no': product.serial_no,
                    'is_rework': product.is_rework,
                    'created_at': product.created_at.strftime('%Y-%m-%d %H:%M:%S')
                }
            })
    except Exception as e:
        logger.error(f"Error adding product to work order {work_order_id}: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': str(e)}), 500

# 获取工单下的产品列表
@quality_inspection_bp.route('/work-order/<int:work_order_id>/products', methods=['GET'])
def get_products(work_order_id):
    try:
        db = DatabaseManager()
        with db.get_session() as session:
            # 检查工单是否存在
            work_order = session.query(QualityInspectionWorkOrder).filter_by(id=work_order_id).first()
            if not work_order:
                return jsonify({'success': False, 'message': '工单不存在'}), 404

            # 获取产品列表
            products = session.query(Product).filter_by(work_order_id=work_order_id).all()

            # 获取每个产品的检验状态
            result = []
            for product in products:
                # 获取产品在各阶段的检验状态
                inspection_statuses = session.query(ProductInspectionStatus).filter_by(product_id=product.id).all()

                # 构建阶段状态字典
                stages = {}
                for stage in ['assembly', 'test', 'packaging']:
                    # 初始化阶段状态
                    stages[stage] = {
                        'first_completed': False,
                        'self_completed': False,
                        'ipqc_completed': False,
                        'stage_completed': False
                    }

                    # 更新各角色的完成状态
                    for status in inspection_statuses:
                        if status.stage == stage:
                            if status.inspector_role == 'first' and status.is_passed:
                                stages[stage]['first_completed'] = True
                            elif status.inspector_role == 'self' and status.is_passed:
                                stages[stage]['self_completed'] = True
                            elif status.inspector_role == 'ipqc' and status.is_passed:
                                stages[stage]['ipqc_completed'] = True

                    # 如果所有角色都已完成，则标记阶段完成
                    if (stages[stage]['first_completed'] and
                        stages[stage]['self_completed'] and
                        stages[stage]['ipqc_completed']):
                        stages[stage]['stage_completed'] = True

                product_data = {
                    'id': product.id,
                    'serial_no': product.serial_no,
                    'is_rework': product.is_rework,
                    'created_at': product.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'stages': stages
                }
                result.append(product_data)

            return jsonify({
                'success': True,
                'products': result
            })
    except Exception as e:
        logger.error(f"Error getting products for work order {work_order_id}: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': str(e)}), 500

# 获取产品状态
@quality_inspection_bp.route('/product-status', methods=['GET'])
def get_product_status():
    try:
        order_no = request.args.get('order_no')
        serial_no = request.args.get('serial_no')

        if not order_no or not serial_no:
            return jsonify({'success': False, 'message': '工单号和SN号不能为空'}), 400

        db = DatabaseManager()
        with db.get_session() as session:
            # 查询工单
            work_order = session.query(QualityInspectionWorkOrder).filter_by(work_order_no=order_no).first()
            if not work_order:
                return jsonify({'success': False, 'message': '工单不存在'}), 404

            # 查询产品
            product = session.query(Product).filter_by(
                work_order_id=work_order.id,
                serial_no=serial_no
            ).first()

            if not product:
                return jsonify({'success': False, 'message': '产品不存在'}), 404

            # 获取产品在各阶段的检验状态
            stages = {}
            for stage in ['assembly', 'test', 'packaging']:
                # 初始化阶段状态
                stages[stage] = {
                    'first': False,
                    'self': False,
                    'ipqc': False,
                    'completed': False
                }

                # 查询各角色的检验状态
                for role in ['first', 'self', 'ipqc']:
                    status = session.query(ProductInspectionStatus).filter_by(
                        product_id=product.id,
                        stage=stage,
                        inspector_role=role
                    ).first()

                    if status and status.is_passed:
                        stages[stage][role] = True

                # 如果所有角色都已完成，则标记阶段完成
                if stages[stage]['first'] and stages[stage]['self'] and stages[stage]['ipqc']:
                    stages[stage]['completed'] = True

            # 构建响应
            result = {
                'id': product.id,
                'serial_no': product.serial_no,
                'work_order_no': work_order.work_order_no,
                'product_type': work_order.product_type.type_name if work_order.product_type else '',
                'created_at': product.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'stages': stages
            }

            return jsonify({
                'success': True,
                'product': result
            })
    except Exception as e:
        logger.error(f"Error getting product status: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': str(e)}), 500

# 检查工单中未完成自检的产品数量
@quality_inspection_bp.route('/work-order/<int:work_order_id>/remaining-products', methods=['GET'])
def check_remaining_products(work_order_id):
    try:
        stage = request.args.get('stage')

        if not stage:
            return jsonify({'success': False, 'message': '缺少阶段参数'}), 400

        db = DatabaseManager()
        with db.get_session() as session:
            # 获取工单下所有产品
            all_products = session.query(Product).filter_by(work_order_id=work_order_id).all()
            total_count = len(all_products)

            if total_count == 0:
                return jsonify({
                    'success': True,
                    'total': 0,
                    'completed': 0,
                    'remaining': 0
                })

            # 获取已完成自检的产品ID列表
            completed_products = session.query(ProductInspectionStatus).filter_by(
                work_order_id=work_order_id,
                stage=stage,
                inspector_role='self',
                is_passed=True
            ).with_entities(ProductInspectionStatus.product_id).all()

            # 转换为ID集合，去重
            completed_product_ids = set([p[0] for p in completed_products])
            completed_count = len(completed_product_ids)

            remaining_count = total_count - completed_count

            return jsonify({
                'success': True,
                'total': total_count,
                'completed': completed_count,
                'remaining': remaining_count
            })
    except Exception as e:
        logger.error(f"Error checking remaining products: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': str(e)}), 500

# 获取工单中产品的检验状态
@quality_inspection_bp.route('/work-order/<int:work_order_id>/inspection-status', methods=['GET'])
def get_work_order_inspection_status(work_order_id):
    try:
        stage = request.args.get('stage')
        role = request.args.get('role', 'self')  # 默认查询自检角色

        if not stage:
            return jsonify({'success': False, 'message': '缺少阶段参数'}), 400

        db = DatabaseManager()
        with db.get_session() as session:
            # 获取工单信息
            work_order = session.query(QualityInspectionWorkOrder).filter_by(id=work_order_id).first()
            if not work_order:
                return jsonify({'success': False, 'message': '工单不存在'}), 404

            # 获取工单下所有产品
            products = session.query(Product).filter_by(work_order_id=work_order_id).all()

            # 获取每个产品的检验状态
            product_status = []
            for product in products:
                # 查询产品在当前阶段和角色的检验状态
                status = session.query(ProductInspectionStatus).filter_by(
                    product_id=product.id,
                    stage=stage,
                    inspector_role=role
                ).first()

                product_status.append({
                    'id': product.id,
                    'serial_no': product.serial_no,
                    'is_completed': status is not None and status.is_passed,
                    'inspector': status.inspector if status else None,
                    'inspection_time': status.inspection_time.strftime('%Y-%m-%d %H:%M:%S') if status and status.inspection_time else None,
                    'submission_type': status.submission_type if status else None  # 添加提交类型
                })

            # 检查该阶段角色是否已经最终提交
            final_submission_exists = session.query(ProductInspectionStatus).filter(
                ProductInspectionStatus.work_order_id == work_order_id,
                ProductInspectionStatus.stage == stage,
                ProductInspectionStatus.inspector_role == role,
                ProductInspectionStatus.submission_type == 'final'
            ).first() is not None
            
            # 检查是否有部分提交
            partial_submission_exists = session.query(ProductInspectionStatus).filter(
                ProductInspectionStatus.work_order_id == work_order_id,
                ProductInspectionStatus.stage == stage,
                ProductInspectionStatus.inspector_role == role,
                ProductInspectionStatus.submission_type == 'partial'
            ).first() is not None

            # 统计已完成和未完成的产品数量
            total_count = len(products)
            completed_count = session.query(ProductInspectionStatus).filter(
                ProductInspectionStatus.work_order_id == work_order_id,
                ProductInspectionStatus.stage == stage,
                ProductInspectionStatus.inspector_role == role,
                ProductInspectionStatus.is_passed == True
            ).distinct(ProductInspectionStatus.product_id).count()

            remaining_count = total_count - completed_count

            return jsonify({
                'success': True,
                'data': {
                    'total_count': total_count,
                    'completed_count': completed_count,
                    'remaining_count': remaining_count,
                    'products': product_status,
                    'is_final_submitted': final_submission_exists,  # 是否已最终提交
                    'has_partial_submission': partial_submission_exists  # 是否有部分提交
                }
            })

    except Exception as e:
        print(f"Error getting inspection status: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取检验状态时发生错误: {str(e)}'
        }), 500

# 创建返工工单
@quality_inspection_bp.route('/rework-work-order', methods=['POST'])
def create_rework_work_order():
    data = request.get_json()
    work_order_no = data.get('work_order_no')
    product_type_id = data.get('product_type_id')

    if not work_order_no or not product_type_id:
        return jsonify({
            'success': False,
            'message': '工单号和产品类型ID不能为空'
        }), 400

    try:
        db = DatabaseManager()
        with db.get_session() as session:
            # 检查工单是否已存在
            work_order = session.query(QualityInspectionWorkOrder)\
                .filter_by(work_order_no=work_order_no)\
                .first()

            if work_order:
                return jsonify({
                    'success': False,
                    'message': '工单号已存在'
                }), 400

            # 创建新的返工工单
            work_order = QualityInspectionWorkOrder(
                work_order_no=work_order_no,
                product_type_id=product_type_id,
                is_rework=True  # 设置为返工工单
            )
            session.add(work_order)
            session.commit()

            return jsonify({
                'success': True,
                'work_order': {
                    'id': work_order.id,
                    'work_order_no': work_order.work_order_no,
                    'product_type_id': work_order.product_type_id,
                    'status': work_order.status,
                    'is_rework': work_order.is_rework
                }
            })
    except Exception as e:
        logger.error(f"Error creating rework work order: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': str(e)}), 500

# 添加返工产品到工单
@quality_inspection_bp.route('/work-order/<int:work_order_id>/rework-product', methods=['POST'])
def add_rework_product(work_order_id):
    try:
        data = request.get_json()
        serial_no = data.get('serial_no')
        original_product_id = data.get('original_product_id')
        original_work_order_id = data.get('original_work_order_id')

        if not serial_no:
            return jsonify({'success': False, 'message': 'SN号不能为空'}), 400

        db = DatabaseManager()
        with db.get_session() as session:
            # 检查工单是否存在且是返工工单
            work_order = session.query(QualityInspectionWorkOrder).filter_by(id=work_order_id).first()
            if not work_order:
                return jsonify({'success': False, 'message': '工单不存在'}), 404

            if not work_order.is_rework:
                return jsonify({'success': False, 'message': '只有返工工单才能添加返工产品'}), 400

            # 检查SN号是否已存在于当前工单
            existing_in_current = session.query(Product).filter_by(
                work_order_id=work_order_id,
                serial_no=serial_no
            ).first()

            if existing_in_current:
                return jsonify({'success': False, 'message': 'SN号已存在于当前工单'}), 400

            # 检查原始产品是否存在
            original_product = None
            if original_product_id:
                original_product = session.query(Product).filter_by(id=original_product_id).first()
            else:
                # 如果没有提供原始产品ID，尝试通过SN号查找
                original_product = session.query(Product).filter_by(serial_no=serial_no).first()

            if not original_product:
                return jsonify({'success': False, 'message': '原始产品不存在，无法创建返工产品'}), 400

            # 创建返工产品
            product = Product(
                work_order_id=work_order_id,
                serial_no=serial_no,
                is_rework=True,  # 设置为返工产品
                original_product_id=original_product.id,
                original_work_order_id=original_product.work_order_id
            )

            session.add(product)
            session.commit()

            return jsonify({
                'success': True,
                'product': {
                    'id': product.id,
                    'serial_no': product.serial_no,
                    'is_rework': product.is_rework,
                    'original_product_id': product.original_product_id,
                    'original_work_order_id': product.original_work_order_id,
                    'created_at': product.created_at.strftime('%Y-%m-%d %H:%M:%S')
                }
            })
    except Exception as e:
        logger.error(f"Error adding rework product to work order {work_order_id}: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': str(e)}), 500

# 获取工单信息（简化版，用于返工产品确认）
@quality_inspection_bp.route('/work-order/<int:work_order_id>/info', methods=['GET'])
def get_work_order_info(work_order_id):
    try:
        db = DatabaseManager()
        with db.get_session() as session:
            work_order = session.query(QualityInspectionWorkOrder).filter_by(id=work_order_id).first()

            if not work_order:
                return jsonify({
                    'success': False,
                    'message': '工单不存在'
                }), 404

            return jsonify({
                'success': True,
                'work_order': {
                    'id': work_order.id,
                    'work_order_no': work_order.work_order_no,
                    'product_type_id': work_order.product_type_id,
                    'status': work_order.status,
                    'is_rework': work_order.is_rework
                }
            })
    except Exception as e:
        logger.error(f"Error getting work order info {work_order_id}: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'message': str(e)}), 500

# 获取未完成的产品
@quality_inspection_bp.route('/work-order/<int:work_order_id>/pending-products', methods=['GET'])
def get_pending_products(work_order_id):
    stage = request.args.get('stage')
    role = request.args.get('role')
    
    if not stage or not role:
        return jsonify({'success': False, 'message': '缺少必要参数'}), 400
    
    try:
        db = DatabaseManager()
        with db.get_session() as session:
            # 查询工单下的所有产品
            all_products = session.query(Product).filter(Product.work_order_id == work_order_id).all()
            
            # 查询已完成检验的产品IDs
            completed_product_ids = session.query(ProductInspectionStatus.product_id).\
                filter(ProductInspectionStatus.work_order_id == work_order_id,
                       ProductInspectionStatus.stage == stage,
                       ProductInspectionStatus.inspector_role == role,
                       ProductInspectionStatus.is_passed == True).\
                distinct().all()
            
            # 将查询结果转换为ID列表
            completed_product_ids = [item[0] for item in completed_product_ids]
            
            # 过滤出未完成的产品
            pending_products = [product for product in all_products 
                               if product.id not in completed_product_ids]
            
            # 检查是否已经最终提交
            final_submission = session.query(ProductInspectionStatus).\
                filter(ProductInspectionStatus.work_order_id == work_order_id,
                       ProductInspectionStatus.stage == stage,
                       ProductInspectionStatus.inspector_role == role,
                       ProductInspectionStatus.submission_type == 'final').\
                first() is not None
            
            # 如果已经最终提交，则不允许继续添加产品
            if final_submission:
                return jsonify({
                    'success': True,
                    'data': {
                        'pending_products': [],
                        'message': '该阶段已完成最终提交，无法添加新产品',
                        'is_final_submitted': True
                    }
                })
            
            # 构建响应数据
            result = {
                'success': True,
                'data': {
                    'pending_products': [
                        {
                            'id': product.id,
                            'serial_no': product.serial_no,
                            'created_at': product.created_at.strftime('%Y-%m-%d %H:%M:%S') if product.created_at else None
                        } 
                        for product in pending_products
                    ],
                    'total_count': len(all_products),
                    'pending_count': len(pending_products),
                    'completed_count': len(completed_product_ids),
                    'is_final_submitted': False
                }
            }
            
            return jsonify(result)
            
    except Exception as e:
        print(f"Error getting pending products: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取未完成产品时发生错误: {str(e)}'
        }), 500

# 注意：以下初始化端点已被移除，数据库初始化应通过脚本完成
# 这些端点在生产环境中不应该存在，因为它们可能导致数据丢失
# 如需初始化数据，请使用数据库迁移脚本或单独的初始化工具
# ===== 跳过检验功能 =====
@quality_inspection_bp.route('/work-order/<int:work_order_id>/skip-all-stages', methods=['POST'])
def skip_all_inspection_stages(work_order_id):
    """
    跳过所有检验阶段，直接将工单标记为完成状态
    这是一个高风险操作，仅在特殊情况下使用
    """
    from models.modelquality_inspection import QualityInspectionWorkOrder
    
    try:
        db = DatabaseManager()
        with db.get_session() as session:
            # 查找工单
            work_order = session.query(QualityInspectionWorkOrder).filter_by(id=work_order_id).first()
            
            if not work_order:
                return jsonify({
                    'success': False,
                    'message': '工单不存在'
                }), 404
            
            # 检查工单的所有阶段是否均已完成
            if (work_order.assembly_stage_completed and
                work_order.test_stage_completed and
                work_order.packaging_stage_completed):
                return jsonify({
                    'success': False,
                    'message': '工单的所有阶段均已是完成状态'
                }), 400
            
            # 更新工单状态：将所有阶段标记为完成
            work_order.assembly_stage_completed = True
            work_order.test_stage_completed = True
            work_order.packaging_stage_completed = True
            work_order.status = 'completed'
            
            # 设置更新时间
            from datetime import datetime
            work_order.updated_at = datetime.utcnow()
            
            session.commit()
            
            print(f"[跳过检验] 工单 {work_order.work_order_no} (ID: {work_order_id}) 已跳过所有检验阶段")
            
            return jsonify({
                'success': True,
                'message': '已成功跳过所有检验阶段',
                'data': {
                    'work_order_no': work_order.work_order_no,
                    'assembly_stage_completed': work_order.assembly_stage_completed,
                    'test_stage_completed': work_order.test_stage_completed,
                    'packaging_stage_completed': work_order.packaging_stage_completed,
                    'status': work_order.status,
                    'updated_at': work_order.updated_at.strftime('%Y-%m-%d %H:%M:%S')
                }
            })
            
    except Exception as e:
        print(f"[跳过检验错误] 工单ID: {work_order_id}, 错误: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'跳过检验时发生错误: {str(e)}'
        }), 500