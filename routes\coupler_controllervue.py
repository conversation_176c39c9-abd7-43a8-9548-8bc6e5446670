"""
耦合器模块Vue版本后端API
遵循Vue版本重构最佳实践规范

功能特点：
1. 完全独立的蓝图，不影响原版本
2. 使用ORM操作替代原生SQL，充分发挥ORM优势
3. 详细的日志标记 [Coupler Vue API] 便于调试
4. 优化的错误处理和参数验证
5. 更好的事务管理和数据一致性保证
6. 支持工单信息查询功能
"""

from flask import Blueprint, jsonify, request, current_app
from jwt import decode
from database.db_manager import DatabaseManager
from datetime import datetime
from models.test_result import CouplerTest, FaultEntry
from models.assembly import CompleteProduct
from models.firmware import FirmwareSerialDetail, DownloadRecord
from models.modelwork_order import WorkOrder
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import and_, or_
import logging

# 配置日志
logger = logging.getLogger(__name__)

# 创建蓝图 - 使用不同的URL前缀避免冲突
coupler_controllervue_bp = Blueprint('coupler_controllervue', __name__)

def get_current_user():
    """获取当前用户信息"""
    token = request.cookies.get('token')
    if token:
        try:
            payload = decode(token, current_app.config['JWT_SECRET'], algorithms=["HS256"])
            return payload.get('username')
        except Exception as e:
            logger.warning(f"[Coupler Vue API] Token解析失败: {str(e)}")
            return None
    return None

def validate_required_fields(data, required_fields):
    """验证必需字段"""
    missing_fields = []
    for field in required_fields:
        if field not in data or data[field] is None or str(data[field]).strip() == '':
            missing_fields.append(field)
    return missing_fields

def safe_int_convert(value, default=0):
    """安全的整数转换"""
    try:
        return int(value) if value is not None else default
    except (ValueError, TypeError):
        return default

def safe_str_convert(value, default='N/A'):
    """安全的字符串转换"""
    if value is None:
        return default
    return str(value).strip() if str(value).strip() else default

# API路由
@coupler_controllervue_bp.route('/get-current-user', methods=['GET'])
def get_current_user_route():
    """获取当前用户信息"""
    try:
        logger.info("[Coupler Vue API] 获取当前用户信息")
        username = get_current_user()
        
        return jsonify({
            'success': True,
            'username': username
        })
    except Exception as e:
        logger.error(f"[Coupler Vue API] 获取用户信息失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': '获取用户信息失败'
        }), 500

@coupler_controllervue_bp.route('/check-sn', methods=['GET'])
def check_sn():
    """检查SN号是否已经绑定PCBA (ORM优化版本)"""
    try:
        sn = request.args.get('sn')
        if not sn:
            logger.warning("[Coupler Vue API] SN检查: SN号参数缺失")
            return jsonify({
                'success': False,
                'message': '请提供SN号'
            }), 400
        
        sn = sn.strip()
        logger.info(f"[Coupler Vue API] 检查SN号: {sn}")
        
        db = DatabaseManager()
        with db.get_session() as session:
            try:
                # 使用ORM查询complete_products表
                result = session.query(CompleteProduct).filter(
                    CompleteProduct.product_sn == sn
                ).first()
                
                if result:
                    logger.info(f"[Coupler Vue API] SN号已绑定PCBA: {sn}")
                    return jsonify({
                        'success': True,
                        'exists': True,
                        'message': 'SN号已绑定PCBA'
                    })
                else:
                    logger.info(f"[Coupler Vue API] SN号未绑定PCBA: {sn}")
                    return jsonify({
                        'success': True,
                        'exists': False,
                        'message': 'SN号未绑定PCBA'
                    })
                    
            except SQLAlchemyError as e:
                logger.error(f"[Coupler Vue API] SN检查数据库错误: {str(e)}")
                session.rollback()
                return jsonify({
                    'success': False,
                    'message': '数据库查询失败'
                }), 500
                
    except Exception as e:
        logger.error(f"[Coupler Vue API] SN检查异常: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'查询失败：{str(e)}'
        }), 500

@coupler_controllervue_bp.route('/get-version-info', methods=['GET'])
def get_version_info():
    """根据产品SN号获取软件版本信息（耦合器模块）(ORM优化版本)
    
    支持两种查询模式：
    1. 新品模式（product_status=new）：三表联查
       product_sn → complete_products → assembly_id → firmware_serial_detail → work_order → download_record
    2. 返工/维修模式（product_status=used/refurbished）：两表联查
       product_sn → firmware_serial_detail → work_order → download_record
    """
    try:
        # 获取查询参数
        product_sn = request.args.get('product_sn')
        product_status = request.args.get('product_status', 'new')  # 默认为新品
        
        if not product_sn:
            logger.warning("[Coupler Vue API] 版本信息查询: 产品SN号参数缺失")
            return jsonify({
                'success': False,
                'message': '请提供产品SN号'
            }), 400
        
        product_sn = product_sn.strip()
        logger.info(f"[Coupler Vue API] 获取版本信息: SN={product_sn}, 模式={product_status}")
        
        db = DatabaseManager()
        with db.get_session() as session:
            try:
                result = None
                
                if product_status == 'new':
                    # 新品模式：三表联查
                    # 1. 从complete_products获取assembly_id
                    # 2. 从firmware_serial_detail获取work_order (使用assembly_id作为firmware_hash)
                    # 3. 从download_record获取software_version和build_time
                    result = session.query(
                        DownloadRecord.software_version,
                        DownloadRecord.build_time
                    ).join(
                        FirmwareSerialDetail, 
                        DownloadRecord.work_order == FirmwareSerialDetail.work_order
                    ).join(
                        CompleteProduct,
                        FirmwareSerialDetail.firmware_hash == CompleteProduct.assembly_id
                    ).filter(
                        CompleteProduct.product_sn == product_sn
                    ).order_by(
                        DownloadRecord.create_time.desc()  # 获取最新的记录
                    ).first()
                
                elif product_status in ['used', 'refurbished']:
                    # 返工/维修模式：两表联查
                    # 1. 从firmware_serial_detail获取work_order (使用product_sn作为firmware_sn)
                    # 2. 从download_record获取software_version和build_time
                    result = session.query(
                        DownloadRecord.software_version,
                        DownloadRecord.build_time
                    ).join(
                        FirmwareSerialDetail,
                        DownloadRecord.work_order == FirmwareSerialDetail.work_order
                    ).filter(
                        FirmwareSerialDetail.firmware_sn == product_sn
                    ).order_by(
                        DownloadRecord.create_time.desc()  # 获取最新的记录
                    ).first()
                
                if result:
                    version_data = {
                        'software_version': result.software_version or '',
                        'build_time': result.build_time or ''
                    }
                    
                    logger.info(f"[Coupler Vue API] 版本信息获取成功: {product_sn} ({product_status}模式)")
                    return jsonify({
                        'success': True,
                        'data': version_data,
                        'message': f'耦合器版本信息获取成功（{product_status}模式）'
                    })
                else:
                    logger.info(f"[Coupler Vue API] 未找到版本信息: {product_sn} ({product_status}模式)")
                    return jsonify({
                        'success': True,
                        'data': {
                            'software_version': '',
                            'build_time': ''
                        },
                        'message': f'未找到相关版本信息（{product_status}模式）'
                    })
                    
            except SQLAlchemyError as e:
                logger.error(f"[Coupler Vue API] 版本信息查询数据库错误: {str(e)}")
                session.rollback()
                return jsonify({
                    'success': False,
                    'message': '数据库查询失败'
                }), 500
                
    except Exception as e:
        logger.error(f"[Coupler Vue API] 版本信息查询异常: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'查询版本信息失败：{str(e)}'
        }), 500

@coupler_controllervue_bp.route('/submit-test', methods=['POST'])
def submit_test():
    """提交耦合器测试数据 (ORM优化版本)"""
    try:
        data = request.get_json()
        logger.info(f"[Coupler Vue API] 接收测试数据提交请求")
        
        # 参数验证
        required_fields = ['tester', 'work_order', 'work_qty', 'pro_model', 'pro_code', 'pro_sn', 'pro_status']
        missing_fields = validate_required_fields(data, required_fields)
        
        if missing_fields:
            logger.warning(f"[Coupler Vue API] 缺少必需字段: {missing_fields}")
            return jsonify({
                'success': False,
                'message': f'缺少必需字段: {", ".join(missing_fields)}'
            }), 400
        
        pro_status = safe_int_convert(data.get('pro_status'))
        if pro_status not in [1, 2, 3]:
            logger.warning(f"[Coupler Vue API] 无效的产品状态: {pro_status}")
            return jsonify({
                'success': False,
                'message': '产品状态必须为1(新品)、2(维修)或3(返工)'
            }), 400

        pro_sn = safe_str_convert(data['pro_sn'], '').strip()
        work_order = safe_str_convert(data['work_order'], '').strip()
        
        logger.info(f"[Coupler Vue API] 处理测试数据: SN={pro_sn}, 工单={work_order}, 状态={pro_status}")
        
        db = DatabaseManager()
        with db.get_session() as session:
            try:
                # 直接使用前端传来的数字值（前端已经正确转换）
                test_results_numeric = {
                    'backplane': safe_int_convert(data.get('backplane'), 1),
                    'body_io': safe_int_convert(data.get('body_io'), 1),
                    'led_tube': safe_int_convert(data.get('led_tube'), 1),
                    'led_bulb': safe_int_convert(data.get('led_bulb'), 1),
                    'net_port': safe_int_convert(data.get('net_port'), 1)
                }
                
                # 收集失败的测试项目（值为2表示失败）
                failed_tests = [k for k, v in test_results_numeric.items() if v == 2]
                test_status = 'pass' if not failed_tests else 'ng'
                
                logger.info(f"[Coupler Vue API] 测试结果统计: 总项目5个, 失败{len(failed_tests)}个, 状态={test_status}")
                
                # 查询现有记录
                existing_record = session.query(CouplerTest).filter(
                    CouplerTest.pro_sn == pro_sn
                ).order_by(CouplerTest.test_time.desc()).first()
                
                # 计算维修和返工次数
                if existing_record:
                    if pro_status == 1:  # 新品不能重复SN
                        logger.warning(f"[Coupler Vue API] 新品SN号重复: {pro_sn}")
                        return jsonify({
                            'success': False,
                            'message': f'产品SN号 {pro_sn} 已存在，新品不能使用重复的SN号'
                        }), 400
                    
                    # 更新现有记录
                    maintenance_count = existing_record.maintenance + (1 if pro_status == 2 else 0)
                    rework_count = existing_record.rework + (1 if pro_status == 3 else 0)
                    
                    # 更新记录字段
                    existing_record.tester = safe_str_convert(data['tester'])
                    existing_record.work_order = work_order
                    existing_record.work_qty = safe_int_convert(data['work_qty'])
                    existing_record.pro_model = safe_str_convert(data['pro_model'])
                    existing_record.pro_code = safe_str_convert(data['pro_code'])
                    existing_record.pro_status = pro_status
                    existing_record.pro_batch = safe_str_convert(data.get('pro_batch'))
                    existing_record.remarks = safe_str_convert(data.get('remarks'))
                    existing_record.maintenance = maintenance_count
                    existing_record.rework = rework_count
                    existing_record.couplersw_version = safe_str_convert(data.get('couplersw_version'))
                    existing_record.coupler_date = safe_str_convert(data.get('coupler_date'))
                    existing_record.test_status = test_status
                    existing_record.test_time = datetime.now()
                    existing_record.product_type = 'coupler_module'
                    
                    # 更新测试结果
                    for field, value in test_results_numeric.items():
                        setattr(existing_record, field, value)
                    
                    # 维修/返工时清除比对相关字段
                    if pro_status in [2, 3]:
                        existing_record.comparison_time = None
                        existing_record.comparison_result = None
                        existing_record.comparison_user = None
                    
                    action_msg = "更新"
                    logger.info(f"[Coupler Vue API] 更新现有记录: SN={pro_sn}, 维修{maintenance_count}次, 返工{rework_count}次")
                
                else:
                    # 创建新记录
                    if pro_status == 1:  # 新品
                        maintenance_count = 0
                        rework_count = 0
                    elif pro_status == 2:  # 维修
                        maintenance_count = 1
                        rework_count = 0
                    else:  # 返工
                        maintenance_count = 0
                        rework_count = 1
                    
                    # 创建新的耦合器测试记录
                    new_record = CouplerTest(
                        tester=safe_str_convert(data['tester']),
                        work_order=work_order,
                        work_qty=safe_int_convert(data['work_qty']),
                        pro_model=safe_str_convert(data['pro_model']),
                        pro_code=safe_str_convert(data['pro_code']),
                        pro_status=pro_status,
                        pro_sn=pro_sn,
                        pro_batch=safe_str_convert(data.get('pro_batch')),
                        remarks=safe_str_convert(data.get('remarks')),
                        maintenance=maintenance_count,
                        rework=rework_count,
                        couplersw_version=safe_str_convert(data.get('couplersw_version')),
                        coupler_date=safe_str_convert(data.get('coupler_date')),
                        test_status=test_status,
                        product_type='coupler_module',
                        **test_results_numeric
                    )
                    
                    session.add(new_record)
                    action_msg = "插入"
                    logger.info(f"[Coupler Vue API] 创建新记录: SN={pro_sn}, 维修{maintenance_count}次, 返工{rework_count}次")
                
                # 如果有失败的测试项目，插入故障记录
                if failed_tests:
                    logger.info(f"[Coupler Vue API] 插入故障记录: {failed_tests}")
                    
                    # 使用ORM创建故障记录
                    fault_record = FaultEntry(
                        tester=safe_str_convert(data['tester']),
                        work_order=work_order,
                        work_qty=safe_int_convert(data['work_qty']),
                        pro_model=safe_str_convert(data['pro_model']),
                        pro_code=safe_str_convert(data['pro_code']),
                        pro_status=pro_status,
                        pro_sn=pro_sn,
                        pro_batch=safe_str_convert(data.get('pro_batch')),
                        remarks=safe_str_convert(data.get('remarks')),
                        rework=rework_count,
                        maintenance=maintenance_count,
                        
                        # 故障类型字段 (0=无故障, 2=有故障)
                        burn_err=0,
                        power_err=0,
                        backplane_err=2 if 'backplane' in failed_tests else 0,
                        body_io_err=2 if 'body_io' in failed_tests else 0,
                        led_tube_err=2 if 'led_tube' in failed_tests else 0,
                        led_bulb_err=2 if 'led_bulb' in failed_tests else 0,
                        net_port_err=2 if 'net_port' in failed_tests else 0,
                        rs485_1_err=0,  # 耦合器没有这些测试项目
                        rs485_2_err=0,
                        rs232_err=0,
                        canbus_err=0,
                        ethercat_err=0,
                        usb_drive_err=0,
                        sd_slot_err=0,
                        debug_port_err=0,
                        dip_switch_err=0,
                        reset_btn_err=0,
                        other_err='N/A'
                    )
                    
                    session.add(fault_record)
                    logger.info(f"[Coupler Vue API] 故障记录ORM插入成功: 失败项目={len(failed_tests)}个")

                # 提交事务
                session.commit()
                
                # 构建返回消息
                msg_parts = [f'测试信息{action_msg}成功']
                if failed_tests:
                    msg_parts.append(f'故障项目: {", ".join(failed_tests)}')
                if maintenance_count > 0:
                    msg_parts.append(f'维修{maintenance_count}次')
                if rework_count > 0:
                    msg_parts.append(f'返工{rework_count}次')
                
                success_message = '，'.join(msg_parts)
                logger.info(f"[Coupler Vue API] 测试数据处理完成: {success_message}")
                
                return jsonify({
                    'success': True,
                    'message': success_message
                })
                
            except SQLAlchemyError as e:
                logger.error(f"[Coupler Vue API] 数据库操作错误: {str(e)}")
                session.rollback()
                return jsonify({
                    'success': False,
                    'message': '数据库操作失败，请重试'
                }), 500
        
    except Exception as e:
        logger.error(f"[Coupler Vue API] 测试数据提交异常: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'提交失败：{str(e)}'
        }), 500 