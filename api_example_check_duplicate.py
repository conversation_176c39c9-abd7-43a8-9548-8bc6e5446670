# ===== 后端API接口实现示例 =====
# 文件名建议: quality_inspection_duplicate_check.py 或集成到现有的 quality_inspection.py 中

"""
产品重复检查API接口实现

此接口用于检查指定产品是否已在特定工单的特定阶段和角色中被最终检验过。
设计原则：安全第一，即使查询失败也不阻塞前端操作。
"""

from flask import Flask, request, jsonify
import logging
import traceback

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_product_duplicate():
    """
    检查产品是否已在当前阶段和角色中被检验过
    
    URL: GET /api/quality-inspection/check-product-duplicate
    
    查询参数:
    - work_order_id: 工单ID (必需)
    - serial_no: 产品序列号 (必需)  
    - stage: 检验阶段，如 assembly/test/packaging (必需)
    - inspector_role: 检验角色，如 first/self/ipqc (必需)
    
    返回格式:
    {
        "success": boolean,
        "already_inspected": boolean,  // true表示已检验过
        "message": string
    }
    """
    try:
        # 获取查询参数
        work_order_id = request.args.get('work_order_id')
        serial_no = request.args.get('serial_no')
        stage = request.args.get('stage')
        inspector_role = request.args.get('inspector_role')
        
        # 参数验证
        if not all([work_order_id, serial_no, stage, inspector_role]):
            logger.warning("参数不完整: work_order_id=%s, serial_no=%s, stage=%s, inspector_role=%s", 
                         work_order_id, serial_no, stage, inspector_role)
            return jsonify({
                'success': True,  # 注意：参数错误时仍返回success=True
                'already_inspected': False,  # 参数错误时返回False，允许继续操作
                'message': '参数不完整，跳过重复检查'
            }), 200
            
        # 记录查询请求（用于调试）
        logger.info("检查产品重复: work_order_id=%s, serial_no=%s, stage=%s, role=%s", 
                   work_order_id, serial_no, stage, inspector_role)
        
        # 查询是否已检验过
        # 这里的查询逻辑需要根据实际的数据库表结构调整
        already_inspected = check_inspection_exists(work_order_id, serial_no, stage, inspector_role)
        
        return jsonify({
            'success': True,
            'already_inspected': already_inspected,
            'message': '检查完成'
        }), 200
        
    except Exception as e:
        # 记录错误但返回允许继续操作的响应
        logger.error("检查产品重复失败: %s", str(e))
        logger.error("错误详情: %s", traceback.format_exc())
        
        # 关键：错误时返回success=True, already_inspected=False
        # 这确保前端不会因为后端错误而阻塞用户操作
        return jsonify({
            'success': True,
            'already_inspected': False,  # 错误时返回false，允许继续操作
            'message': '检查失败，允许继续操作'
        }), 200  # 返回200状态码，避免前端捕获为网络错误


def check_inspection_exists(work_order_id, serial_no, stage, inspector_role):
    """
    实际的数据库查询逻辑
    
    返回: boolean - True表示已检验过，False表示未检验过
    """
    try:
        # 方案1: 如果使用 inspection_statuses 表
        query_sql = """
            SELECT COUNT(*) as count
            FROM inspection_statuses 
            WHERE work_order_id = %s 
            AND product_serial_no = %s 
            AND stage = %s 
            AND inspector_role = %s
            AND is_final = true
        """
        
        # 方案2: 如果使用 inspection_records 和 products 表关联
        # query_sql = """
        #     SELECT COUNT(*) as count
        #     FROM inspection_records ir
        #     JOIN products p ON ir.product_id = p.id
        #     WHERE ir.work_order_id = %s 
        #     AND p.serial_no = %s 
        #     AND ir.stage = %s 
        #     AND ir.inspector_role = %s
        #     AND ir.is_final_submission = true
        # """
        
        # 这里需要根据实际的数据库连接方式执行查询
        # 示例使用伪代码，实际实现需要替换为具体的数据库操作
        
        # === 伪代码开始 ===
        # result = db.execute(query_sql, (work_order_id, serial_no, stage, inspector_role))
        # count = result.fetchone()['count']
        # return count > 0
        # === 伪代码结束 ===
        
        # 临时返回False，实际使用时请替换为真实的数据库查询
        logger.info("执行数据库查询 (示例): work_order_id=%s, serial_no=%s, stage=%s, role=%s", 
                   work_order_id, serial_no, stage, inspector_role)
        
        # TODO: 替换为实际的数据库查询逻辑
        return False
        
    except Exception as e:
        logger.error("数据库查询失败: %s", str(e))
        # 数据库查询失败时返回False，允许继续操作
        return False


# ===== Flask路由注册示例 =====
# 如果使用Flask框架，可以这样注册路由：

"""
from flask import Flask
app = Flask(__name__)

@app.route('/api/quality-inspection/check-product-duplicate', methods=['GET'])
def api_check_product_duplicate():
    return check_product_duplicate()
"""

# ===== FastAPI路由注册示例 =====
# 如果使用FastAPI框架，可以这样注册路由：

"""
from fastapi import FastAPI, Query
app = FastAPI()

@app.get("/api/quality-inspection/check-product-duplicate")
async def api_check_product_duplicate(
    work_order_id: str = Query(...),
    serial_no: str = Query(...),
    stage: str = Query(...),
    inspector_role: str = Query(...)
):
    return check_product_duplicate()
"""

# ===== 数据库表结构参考 =====
"""
假设的数据库表结构 (根据实际情况调整):

-- 方案1: 使用 inspection_statuses 表
CREATE TABLE inspection_statuses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    work_order_id VARCHAR(50) NOT NULL,
    product_serial_no VARCHAR(100) NOT NULL,
    stage ENUM('assembly', 'test', 'packaging') NOT NULL,
    inspector_role ENUM('first', 'self', 'ipqc') NOT NULL,
    inspector VARCHAR(100) NOT NULL,
    inspection_time DATETIME NOT NULL,
    is_final BOOLEAN DEFAULT FALSE,
    INDEX idx_duplicate_check (work_order_id, product_serial_no, stage, inspector_role)
);

-- 方案2: 使用现有的 inspection_records 和 products 表
-- 需要确保有合适的索引来优化查询性能
"""

print("后端API接口实现示例已生成")
print("请根据实际的数据库结构和框架调整代码")
print("重要: 确保数据库查询有适当的索引以保证性能")