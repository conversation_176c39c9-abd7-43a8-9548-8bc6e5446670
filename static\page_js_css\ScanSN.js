// SN号扫描相关功能

// 使用SelfInspection.js中定义的STAGES和ROLES常量
// 不再重新定义这些常量

// 全局变量，用于存储当前扫描的产品列表
// 使用window对象来避免重复声明
if (typeof window.currentScannedProducts === 'undefined') {
    window.currentScannedProducts = [];
}

// 使用window对象的引用，不再创建新的变量
// 这样可以避免重复声明错误

// 显示SN号扫描界面
function showScanSerialNumberUI(stage, role) {
    console.log('显示SN号扫描界面', stage, role);

    // 确保获取当前用户信息
    getCurrentUserInfo();

    // 获取容器元素
    let container;

    // 首先尝试使用统一的ID格式获取容器
    container = document.getElementById(`${stage}-${role}`);

    // 如果找不到，尝试获取检验内容容器
    if (!container) {
        container = document.getElementById('inspection-content');
    }

    // 如果仍然找不到，尝试获取自检内容容器
    if (!container) {
        // 查找当前活动的检验内容区域
        const activeContent = document.querySelector('.self-inspection__inspection-content--active');
        if (activeContent) {
            container = activeContent;
        } else {
            console.error('找不到有效的容器元素');
            // 尝试创建一个临时容器
            const tempContainer = document.createElement('div');
            tempContainer.id = `${stage}-${role}-temp`;
            tempContainer.className = 'self-inspection__inspection-content self-inspection__inspection-content--active';

            // 查找父容器
            const parentContainer = document.getElementById(`${stage}-content`);
            if (parentContainer) {
                parentContainer.appendChild(tempContainer);
                container = tempContainer;
                console.log(`已创建临时容器: ${tempContainer.id}`);
            } else {
                console.error('无法创建临时容器，找不到父容器');
                return;
            }
        }
    }

    // 清空当前扫描的产品列表
    window.currentScannedProducts = [];

    // 获取工单号和工单ID
    const orderNoElement = document.getElementById('orderNo');
    const orderNo = orderNoElement?.value || '未指定';

    // 确保工单ID已设置
    if (typeof inspectionData !== 'undefined' && !inspectionData.currentWorkOrderId) {
        // 如果工单ID未设置，尝试创建工单
        if (typeof createWorkOrder === 'function' && orderNo !== '未指定' && inspectionData.currentProductTypeId) {
            console.log('工单ID未设置，尝试创建工单');
            createWorkOrder(orderNo);
            showNotification('info', '正在创建工单', '请稍后再试');
            return;
        } else {
            console.error('无法创建工单，缺少必要信息');
            showNotification('error', '无法扫描SN号', '请先填写工单号并选择产品类型');
            return;
        }
    }

    // 构建界面HTML
    let html = `
        <div class="scan-sn-container">
            <div class="scan-sn-header">
                <h3>请扫描产品SN号</h3>
                <div class="scan-sn-info">
                    <span>工单号: ${orderNo}</span>
                    <span>阶段: ${getStageDisplayName(stage)}</span>
                    <span>角色: ${getRoleDisplayName(role)}</span>
                </div>
            </div>

            <div class="scan-input-group">
                <input type="text" id="sn-input" placeholder="扫描或输入SN号" autofocus>
                <button class="scan-confirm-btn" onclick="processScanedSN()">确认</button>
            </div>

            <div class="scanned-products">
                <div class="scanned-products-header">
                    <h4>已扫描产品 (<span id="scanned-count">0</span>)</h4>
                    <button class="clear-all-btn" onclick="clearScannedProducts()">清空</button>
                </div>
                <div id="scanned-product-list" class="scanned-product-list"></div>
            </div>

            <div class="scan-actions">
                <button class="btn-primary" id="start-inspection-btn" onclick="startInspection('${stage}', '${role}')" disabled>
                    开始检验
                </button>

                ${role === window.ROLES.SELF ? `
                    <div class="self-inspection-note">
                        <i class="fa fa-info-circle"></i> 自检需要检验工单下的每个产品
                    </div>
                ` : `
                    <div class="sampling-note">
                        <i class="fa fa-info-circle"></i> ${role === window.ROLES.FIRST ? '首检' : 'IPQC'}可以抽检部分产品
                    </div>
                `}
            </div>
        </div>
    `;

    container.innerHTML = html;

    // 添加扫描输入框的回车事件
    const snInput = document.getElementById('sn-input');
    if (snInput) {
        snInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                processScanedSN();
            }
        });

        // 自动聚焦到输入框
        setTimeout(() => {
            snInput.focus();
        }, 100);
    }

    // 禁用开始检验按钮（等待扫描产品）
    const startBtn = document.getElementById('start-inspection-btn');
    if (startBtn) {
        startBtn.disabled = true;
    }
}

// 处理扫描的SN号
async function processScanedSN() {
    const snInput = document.getElementById('sn-input');
    if (!snInput) return;

    const serialNo = snInput.value.trim();

    if (!serialNo) {
        showNotification('warning', '请输入SN号');
        return;
    }
    
    // 新增步骤：首先验证SN号的订货号是否正确
    if (typeof inspectionData !== 'undefined' && inspectionData.workOrderDetails) {
        const productCode = inspectionData.workOrderDetails.productCode;
        const isOrderNumberValid = await validateSnForOrderNumber(serialNo, productCode);
        if (!isOrderNumberValid) {
            snInput.value = '';
            snInput.focus();
            return; // 订货号不匹配，中断后续所有操作
        }
    } else {
        showNotification('error', '无法验证订货号', '未能获取到工单详情');
        return;
    }

    // 添加SN号格式验证
    console.log('扫描的SN号:', serialNo);
    console.log('SN号长度:', serialNo.length);
    console.log('SN号字符:', Array.from(serialNo).map(c => c.charCodeAt(0)));
    
    // 检查SN号是否包含特殊字符或不可见字符
    const cleanSerialNo = serialNo.replace(/[^\x20-\x7E]/g, ''); // 移除非打印字符
    if (cleanSerialNo !== serialNo) {
        console.warn('SN号包含特殊字符，原始:', serialNo, '清理后:', cleanSerialNo);
        showNotification('warning', 'SN号包含无效字符', '请检查SN号格式');
        return;
    }
    
    // SN号长度验证
    
    if (typeof inspectionData !== 'undefined' && 
        inspectionData.workOrderDetails && 
        inspectionData.workOrderDetails.snLength > 0) {
        
        const expectedLength = inspectionData.workOrderDetails.snLength;
        if (serialNo.length !== expectedLength) {
            showNotification('error', 'SN号长度错误', 
                `SN号长度必须为${expectedLength}位，当前长度为${serialNo.length}位`);
            return;
        }
    }
    

    // 检查是否已扫描过该SN号
    if (window.currentScannedProducts.some(p => p.serial_no === serialNo)) {
        showNotification('warning', '该SN号已扫描');
        snInput.value = '';
        snInput.focus();
        return;
    }

    // 确保工单ID已设置
    if (typeof inspectionData === 'undefined' || !inspectionData.currentWorkOrderId) {
        console.error('工单ID未设置，无法验证SN号');
        console.error('inspectionData:', typeof inspectionData !== 'undefined' ? inspectionData : 'undefined');
        showNotification('error', '无法验证SN号', '请先填写工单号并选择产品类型');
        return;
    }

    console.log('检查SN号是否属于当前工单', inspectionData.currentWorkOrderId, serialNo);

    // 检查是否为返工工单
    const isReworkWorkOrder = typeof inspectionData !== 'undefined' && inspectionData.isReworkWorkOrder === true;
    console.log('是否为返工工单:', isReworkWorkOrder);

    // 先检查SN号是否已在当前已扫描列表中
    const alreadyScanned = window.currentScannedProducts.find(p => p.serial_no === serialNo);
    if (alreadyScanned) {
        console.log('SN号已在扫描列表中:', alreadyScanned);
        showNotification('warning', '该SN号已扫描过', '请检查扫描列表');
        snInput.value = '';
        snInput.focus();
        return;
    }

    // 检查SN号是否属于当前工单
    checkSerialNumber(inspectionData.currentWorkOrderId, serialNo)
        .then(result => {
            console.log('SN号检查结果:', result);
            
            if (result.success) {
                if (result.exists) {
                    // SN号已存在于当前工单，直接添加到扫描列表
                    console.log('SN号已存在于当前工单，直接添加到扫描列表');
                    addToScannedList(result.product_id, serialNo, result.is_rework);
                } else if (result.exists_in_other_work_order && isReworkWorkOrder) {
                    // 返工工单且SN号存在于其他工单，直接添加返工产品
                    console.log('自动添加返工产品', serialNo, result.original_product_id);
                    addReworkProduct(serialNo, result.original_product_id, result.original_work_order_id);
                } else if (isReworkWorkOrder) {
                    /*// 返工工单但SN号不存在于任何工单，提示错误.注释掉之后就允许添加为新的（返工）产品
                    showNotification('error', 'SN号无效', '返工工单只能添加已存在的SN号');*/
                    // 返工工单但SN号不存在于任何工单，现在允许添加为新的（返工）产品
                    console.log('返工工单，SN号为全新，自动添加为新返工产品', serialNo);
                    addProductToWorkOrder(inspectionData.currentWorkOrderId, serialNo);
                } else {
                    // 普通工单，直接添加新产品
                    console.log('自动添加新产品', serialNo);
                    addProductToWorkOrder(inspectionData.currentWorkOrderId, serialNo);
                }
                
                // 清空输入框并聚焦
                snInput.value = '';
                snInput.focus();
            } else {
                console.error('SN号验证失败:', result);
                showNotification('error', '验证SN号失败', result.message);
            }
        })
        .catch(error => {
            console.error('Error checking serial number:', error);
            showNotification('error', '验证SN号失败', '网络错误');
        });
}

// 检查SN号是否存在于工单中
function checkSerialNumber(workOrderId, serialNo) {
    return fetch(`/api/quality-inspection/check-serial-number?work_order_id=${workOrderId}&serial_no=${serialNo}`)
        .then(response => response.json())
        .catch(error => {
            console.error('Error checking serial number:', error);
            return { success: false, message: '网络错误' };
        });
}

// 函数已删除，由直接调用addProductToWorkOrder代替

// 直接添加返工产品
function addReworkProduct(serialNo, originalProductId, originalWorkOrderId) {
    // 确保工单ID已设置
    if (typeof inspectionData === 'undefined' || !inspectionData.currentWorkOrderId) {
        console.error('工单ID未设置，无法添加返工产品');
        showNotification('error', '无法添加返工产品', '请先填写工单号并选择产品类型');
        return;
    }

    console.log('添加返工产品到工单', inspectionData.currentWorkOrderId, serialNo, originalProductId);

    // 调用专门的返工产品API
    const encodedWorkOrderId = encodeURIComponent(inspectionData.currentWorkOrderId);
    fetch(`/api/quality-inspection/work-order/${encodedWorkOrderId}/rework-product`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            serial_no: serialNo,
            original_product_id: originalProductId,
            original_work_order_id: originalWorkOrderId
        })
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(errorData => {
                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
            }).catch(() => {
                throw new Error(`HTTP error! status: ${response.status}`);
            });
        }
        return response.json();
    })
    .then(result => {
        if (result.success) {
            console.log('返工产品添加成功', result);
            // 添加到已扫描列表，传递返工标识
            addToScannedList(result.product.id, serialNo, true);

            // 添加到产品列表
            if (typeof inspectionData !== 'undefined' && Array.isArray(inspectionData.products)) {
                inspectionData.products.push(result.product);
            }
            // 不显示成功提示弹窗
        } else {
            console.error('添加返工产品失败', result);
            showNotification('error', '添加返工产品失败', result.message || '未知错误');
        }
    })
    .catch(error => {
        console.error('Error adding rework product:', error);
        showNotification('error', '添加返工产品失败', error.message || '网络错误');
        
        // 清空输入框并聚焦
        const snInput = document.getElementById('sn-input');
        if (snInput) {
            snInput.value = '';
            snInput.focus();
        }
    });
}

// 添加产品到工单
function addProductToWorkOrder(workOrderId, serialNo, forceRework = false) {
    if (!workOrderId) {
        console.error('工单ID为空，无法添加产品');
        showNotification('error', '无法添加产品', '工单ID为空');
        return;
    }

    // 检查是否为返工工单或强制设置为返工产品
    const isReworkWorkOrder = typeof inspectionData !== 'undefined' && inspectionData.isReworkWorkOrder === true;
    const isRework = forceRework || isReworkWorkOrder;

    // 确保工单ID被正确编码
    const encodedWorkOrderId = encodeURIComponent(workOrderId);
    
    // 准备请求数据
    const requestData = {
        serial_no: serialNo,
        is_rework: isRework
    };
    
    console.log(`正在添加产品到工单: /api/quality-inspection/work-order/${encodedWorkOrderId}/products`);
    console.log('请求数据:', requestData);
    console.log(`工单ID: ${workOrderId}, SN号: ${serialNo}, 返工标识: ${isRework}`);

    fetch(`/api/quality-inspection/work-order/${encodedWorkOrderId}/products`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        console.log('响应状态:', response.status);
        console.log('响应头:', response.headers);
        
        if (!response.ok) {
            // 尝试获取错误信息
            return response.json().then(errorData => {
                console.error('服务器返回错误:', errorData);
                
                // 特殊处理SN号重复的情况
                if (errorData.message && errorData.message.includes('SN号已存在于当前工单')) {
                    console.log('SN号重复，尝试从工单中查找该产品');
                    return findExistingProductInWorkOrder(workOrderId, serialNo);
                }
                
                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
            }).catch(jsonError => {
                console.error('解析错误响应失败:', jsonError);
                
                // 如果是SN号重复错误，尝试查找现有产品
                if (response.status === 400) {
                    console.log('400错误，可能是SN号重复，尝试查找现有产品');
                    return findExistingProductInWorkOrder(workOrderId, serialNo);
                }
                
                throw new Error(`HTTP error! status: ${response.status}`);
            });
        }
        return response.json();
    })
    .then(result => {
        if (result && result.success) {
            console.log('产品添加成功或找到现有产品', result);
            // 添加到已扫描列表，传递返工标识
            addToScannedList(result.product.id, serialNo, result.product.is_rework);

            // 添加到产品列表
            if (typeof inspectionData !== 'undefined' && Array.isArray(inspectionData.products)) {
                inspectionData.products.push(result.product);
            }
        } else {
            console.error('处理产品失败', result);
            showNotification('error', '添加产品失败', result?.message || '未知错误');
        }
    })
    .catch(error => {
        console.error('Error adding product:', error);
        showNotification('error', '添加产品失败', error.message || '网络错误');
    });
}

// 新增：查找工单中已存在的产品
function findExistingProductInWorkOrder(workOrderId, serialNo) {
    console.log('查找工单中的现有产品:', workOrderId, serialNo);
    
    const encodedWorkOrderId = encodeURIComponent(workOrderId);
    return fetch(`/api/quality-inspection/work-order/${encodedWorkOrderId}/products`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.products) {
                const existingProduct = data.products.find(p => p.serial_no === serialNo);
                if (existingProduct) {
                    console.log('找到现有产品:', existingProduct);
                    return {
                        success: true,
                        product: existingProduct
                    };
                } else {
                    console.warn('在产品列表中未找到该SN号:', serialNo);
                    throw new Error('产品不存在于工单中');
                }
            } else {
                console.error('获取工单产品列表失败:', data);
                throw new Error(data.message || '获取产品列表失败');
            }
        })
        .catch(error => {
            console.error('查找现有产品失败:', error);
            throw error;
        });
}

// 添加到已扫描列表
function addToScannedList(productId, serialNo, isRework = false) {
    // 检查是否已存在相同的SN号
    const existingIndex = window.currentScannedProducts.findIndex(p => p.serial_no === serialNo);
    if (existingIndex !== -1) {
        console.log('SN号已存在于扫描列表中，更新产品信息:', serialNo);
        // 更新现有产品信息而不是添加重复项
        window.currentScannedProducts[existingIndex] = {
            id: productId,
            serial_no: serialNo,
            is_rework: isRework
        };
    } else {
        // 添加新产品到当前扫描列表
        window.currentScannedProducts.push({
            id: productId,
            serial_no: serialNo,
            is_rework: isRework
        });
        console.log('新产品已添加到扫描列表:', {id: productId, serial_no: serialNo, is_rework: isRework});
    }

    // 更新界面
    updateScannedProductList();

    // 清空输入框并聚焦
    const snInput = document.getElementById('sn-input');
    if (snInput) {
        snInput.value = '';
        snInput.focus();
    }

    // 启用开始检验按钮
    const startBtn = document.getElementById('start-inspection-btn');
    if (startBtn) {
        startBtn.disabled = false;
    }
    
    // 显示成功提示（但不阻断流程）
    console.log(`SN号 ${serialNo} 已${existingIndex !== -1 ? '更新' : '添加'}到扫描列表`);
}

// 更新已扫描产品列表
function updateScannedProductList() {
    const container = document.getElementById('scanned-product-list');
    const countElement = document.getElementById('scanned-count');

    if (!container || !countElement) return;

    // 更新计数
    countElement.textContent = window.currentScannedProducts.length;

    // 更新列表
    container.innerHTML = '';

    if (window.currentScannedProducts.length === 0) {
        container.innerHTML = '<p class="no-products">暂无扫描产品</p>';
        return;
    }

    window.currentScannedProducts.forEach((product, index) => {
        const item = document.createElement('div');
        item.className = 'scanned-product-item';

        // 如果是返工产品，添加返工标识
        const reworkBadge = product.is_rework ? '<span class="rework-badge">返工</span>' : '';

        item.innerHTML = `
            <span class="product-sn">${product.serial_no}${reworkBadge}</span>
            <button class="remove-product-btn" onclick="removeScannedProduct(${index})">×</button>
        `;
        container.appendChild(item);
    });
}

// 移除已扫描产品
function removeScannedProduct(index) {
    if (index >= 0 && index < window.currentScannedProducts.length) {
        window.currentScannedProducts.splice(index, 1);
        updateScannedProductList();

        // 如果没有扫描产品，禁用开始检验按钮
        if (window.currentScannedProducts.length === 0) {
            const startBtn = document.getElementById('start-inspection-btn');
            if (startBtn) {
                startBtn.disabled = true;
            }
        }
    }
}

// 清空已扫描产品
function clearScannedProducts() {
    if (window.currentScannedProducts.length === 0) return;

    SweetAlert.confirm('确定要清空已扫描的产品列表吗？').then(confirmed => {
        if (confirmed) {
            window.currentScannedProducts = [];
            updateScannedProductList();

            // 禁用开始检验按钮
            const startBtn = document.getElementById('start-inspection-btn');
            if (startBtn) {
                startBtn.disabled = true;
            }
        }
    });
}

// 开始检验
function startInspection(stage, role) {
    if (window.currentScannedProducts.length === 0) {
        showNotification('warning', '请至少扫描一个产品');
        return;
    }

    // 如果是自检，检查是否有未扫描的产品
    if (role === window.ROLES.SELF) {
        checkRemainingProducts(inspectionData.currentWorkOrderId, stage)
            .then(result => {
                if (result.success && result.remaining > 0) {
                    SweetAlert.custom({
                        title: '未完成扫描',
                        text: `当前工单还有 ${result.remaining} 个产品未扫描，自检需要检验所有产品，是否继续？`,
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonText: '继续检验',
                        cancelButtonText: '返回扫描'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            proceedToInspection(stage, role);
                        }
                    });
                } else {
                    proceedToInspection(stage, role);
                }
            })
            .catch(error => {
                console.error('Error checking remaining products:', error);
                // 出错时也继续检验
                proceedToInspection(stage, role);
            });
    } else {
        // 首检和IPQC直接进入检验界面
        proceedToInspection(stage, role);
    }
}

// 检查工单中未完成自检的产品数量
function checkRemainingProducts(workOrderId, stage) {
    // 确保工单ID被正确编码
    const encodedWorkOrderId = encodeURIComponent(workOrderId);
    return fetch(`/api/quality-inspection/work-order/${encodedWorkOrderId}/remaining-products?stage=${stage}`)
        .then(response => response.json())
        .catch(error => {
            console.error('Error checking remaining products:', error);
            return { success: false, message: '网络错误' };
        });
}

// 进入检验界面
function proceedToInspection(stage, role) {
    console.log('进入检验界面', stage, role);

    // 保存当前选中的产品ID列表
    if (typeof inspectionData !== 'undefined') {
        inspectionData.selectedProductIds = window.currentScannedProducts.map(p => p.id);
        console.log('已选择产品ID列表:', inspectionData.selectedProductIds);

        // 如果存在loadInspectionItems函数，调用它
        if (typeof loadInspectionItems === 'function') {
            const productTypeId = inspectionData.currentProductTypeId;

            if (productTypeId) {
                console.log('加载检验项目:', productTypeId, stage, role);
                loadInspectionItems(productTypeId, stage, role);
            } else {
                console.error('产品类型ID未设置，无法加载检验项目');
                showNotification('error', '无法加载检验项目', '产品类型ID未设置');
            }
        }
    } else {
        console.warn('inspectionData未定义，无法保存产品ID列表');
    }

    // 返回到检验界面
    try {
        // 不再需要转换阶段和角色，直接使用后端阶段和角色名称
        let frontendStage = stage;
        let frontendRole = role;

        console.log('转换后的阶段和角色:', frontendStage, frontendRole);

        // 确保所有DOM元素都已正确创建
        if (typeof window.ensureAllDOMElementsExist === 'function') {
            window.ensureAllDOMElementsExist();
        } else {
            console.warn('ensureAllDOMElementsExist函数未定义，无法确保DOM元素存在');
        }

        // 首先尝试使用switchTab函数
        if (typeof switchTab === 'function') {
            console.log('使用switchTab函数返回检验界面');
            switchTab(frontendStage, frontendRole);
        } else {
            console.warn('switchTab函数未定义，尝试其他方法返回检验界面');

            // 尝试查找对应的选项卡
            const tabId = `${frontendStage}-${frontendRole}-tab`;
            const tab = document.getElementById(tabId);

            if (tab) {
                // 如果找到选项卡，点击它
                console.log('找到选项卡，点击它:', tabId);
                tab.click();
            } else {
                console.warn('无法找到选项卡，尝试直接显示检验内容');

                // 尝试直接显示检验内容
                const contentId = `${frontendStage}-${frontendRole}`;
                const content = document.getElementById(contentId);

                if (content) {
                    console.log('找到内容元素，显示它:', contentId);
                    // 隐藏所有内容
                    const allContents = document.querySelectorAll('.self-inspection__inspection-content');
                    allContents.forEach(c => c.classList.remove('self-inspection__inspection-content--active'));

                    // 显示当前内容
                    content.classList.add('self-inspection__inspection-content--active');
                } else {
                    console.error('找不到内容元素:', contentId);

                    // 最后的尝试：重新加载页面
                    console.log('尝试重新加载页面');
                    showNotification('info', '正在重新加载页面', '请稍候...');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                }
            }
        }

        // 确保操作按钮被正确渲染
        setTimeout(() => {
            renderActionButtons(frontendStage, frontendRole);
        }, 500);
    } catch (error) {
        console.error('返回检验界面时出错:', error);
        showNotification('error', '返回检验界面失败', '请刷新页面重试');
    }

    // 不显示已选择产品的成功提示弹窗
    console.log(`已选择${window.currentScannedProducts.length}个产品进行检验`);
}

// 渲染操作按钮
function renderActionButtons(stage, role) {
    console.log('渲染操作按钮', stage, role);

    // 首先渲染全选按钮
    renderSelectAllButton(stage, role);

    // 然后渲染操作人员输入框
    renderOperatorInput(stage, role);

    // 最后渲染操作按钮和附件上传按钮
    renderActionAndAttachmentButtons(stage, role);
}

// 渲染全选按钮
function renderSelectAllButton(stage, role) {
    console.log('渲染全选按钮', stage, role);

    // 使用统一的容器ID格式
    const selectAllContainerSelector = `#${stage}-${role} .self-inspection__select-all`;
    const selectAllContainer = document.querySelector(selectAllContainerSelector);

    if (!selectAllContainer) {
        console.warn(`找不到全选按钮容器: ${selectAllContainerSelector}`);

        // 查找父容器
        const parentContainer = document.getElementById(`${stage}-${role}`);

        if (parentContainer) {
            console.log('找到父容器，尝试创建全选按钮容器');

            // 检查是否已经有全选按钮容器
            let existingSelectAllContainer = parentContainer.querySelector('.self-inspection__select-all');

            if (!existingSelectAllContainer) {
                // 创建全选按钮容器
                const newSelectAllContainer = document.createElement('div');
                newSelectAllContainer.className = 'self-inspection__select-all';

                // 直接使用阶段和角色名称
                // 检查是否已提交
                const isSubmitted = inspectionData.pageState.inspectionSubmissions[stage]?.[role]?.isSubmitted || false;

                // 创建全选按钮
                newSelectAllContainer.innerHTML = `
                    <div class="self-inspection__checkbox-container">
                        <input type="checkbox" id="${stage}-${role}-selectAll" class="self-inspection__checkbox" ${isSubmitted ? 'disabled' : ''} onchange="toggleSelectAll('${stage}', '${role}')">
                        <label for="${stage}-${role}-selectAll" class="self-inspection__checkbox-label">全选</label>
                    </div>
                `;

                // 添加到父容器的开头
                if (parentContainer.firstChild) {
                    parentContainer.insertBefore(newSelectAllContainer, parentContainer.firstChild);
                } else {
                    parentContainer.appendChild(newSelectAllContainer);
                }

                console.log('已创建全选按钮容器');

                // 更新全选状态
                updateSelectAllStatus(stage, role);
            } else {
                console.log('已存在全选按钮容器，无需创建');
            }
        } else {
            console.error(`找不到父容器: ${stage}-${role} 或 ${stage}${role}`);
        }
    } else {
        console.log('找到全选按钮容器，检查是否需要更新');

        // 更新全选状态
        updateSelectAllStatus(stage, role);
    }
}

// 渲染操作人员输入框
function renderOperatorInput(stage, role) {
    console.log('渲染操作人员输入框', stage, role);

    // 使用统一的容器ID格式
    const operatorContainerSelector = `#${stage}-${role}-operator`;
    const operatorContainer = document.querySelector(operatorContainerSelector);

    if (!operatorContainer) {
        console.warn(`找不到操作人员输入框容器: ${operatorContainerSelector}`);

        // 查找父容器
        const parentContainer = document.getElementById(`${stage}-${role}`);

        if (parentContainer) {
            console.log('找到父容器，尝试创建操作人员输入框容器');

            // 检查是否已经有操作人员输入框容器
            let existingOperatorContainer = parentContainer.querySelector('.self-inspection__operator-info');

            if (!existingOperatorContainer) {
                // 创建操作人员输入框容器
                const newOperatorContainer = document.createElement('div');
                newOperatorContainer.className = 'self-inspection__operator-info';
                newOperatorContainer.id = `${stage}-${role}-operator`;

                // 直接使用阶段和角色名称
                // 检查是否已提交
                const isSubmitted = inspectionData.pageState.inspectionSubmissions[stage]?.[role]?.isSubmitted || false;

                if (isSubmitted) {
                    const operator = inspectionData.pageState.inspectionSubmissions[stage][role].operator;
                    const submittedAt = inspectionData.pageState.inspectionSubmissions[stage][role].submittedAt;

                    newOperatorContainer.classList.add('self-inspection__operator-info--submitted');
                    newOperatorContainer.innerHTML = `
                        <div class="self-inspection__operator-header">
                            <span class="self-inspection__operator-label">操作人员: ${operator}</span>
                            <span class="self-inspection__submitted-tag">已提交</span>
                        </div>
                        <div class="self-inspection__operator-time">提交时间: ${submittedAt}</div>
                    `;
                } else {
                    // 获取当前登录用户名作为默认值
                    const defaultOperator = window.currentLoggedInUser || '';

                    newOperatorContainer.innerHTML = `
                        <div class="self-inspection__operator-input">
                            <label for="${stage}-${role}-operator-input">操作人员:</label>
                            <input type="text" id="${stage}-${role}-operator-input" value="${defaultOperator}" placeholder="请输入操作人员姓名" readonly>
                        </div>
                    `;
                }

                // 添加到全选按钮之后
                const selectAllContainer = parentContainer.querySelector('.self-inspection__select-all');
                if (selectAllContainer && selectAllContainer.nextSibling) {
                    parentContainer.insertBefore(newOperatorContainer, selectAllContainer.nextSibling);
                } else {
                    parentContainer.appendChild(newOperatorContainer);
                }

                console.log('已创建操作人员输入框容器');
            } else {
                console.log('已存在操作人员输入框容器，无需创建');
            }
        } else {
            console.error(`找不到父容器: ${stage}-${role} 或 ${stage}${role}`);
        }
    } else {
        console.log('找到操作人员输入框容器，无需更新');
    }
}

// 渲染操作按钮和附件上传按钮
function renderActionAndAttachmentButtons(stage, role) {
    console.log('渲染操作按钮和附件上传按钮', stage, role);

    // 使用统一的容器ID格式
    const actionBarSelector = `#${stage}-${role} .self-inspection__action-bar`;
    const actionBar = document.querySelector(actionBarSelector);

    if (!actionBar) {
        console.error(`找不到操作按钮容器: ${actionBarSelector}`);

        // 查找父容器
        const parentContainer = document.getElementById(`${stage}-${role}`);

        if (parentContainer) {
            console.log('找到父容器，尝试创建操作按钮容器');

            // 检查是否已经有操作按钮容器
            let existingActionBar = parentContainer.querySelector('.self-inspection__action-bar');

            if (!existingActionBar) {
                // 创建操作按钮容器
                const newActionBar = document.createElement('div');
                newActionBar.className = 'self-inspection__action-bar';

                // 直接使用阶段和角色名称
                // 检查是否已提交
                const isSubmitted = inspectionData.pageState.inspectionSubmissions[stage]?.[role]?.isSubmitted || false;

                if (isSubmitted) {
                    newActionBar.innerHTML = `
                        <button class="self-inspection__btn self-inspection__btn--reset self-inspection__btn--disabled" disabled>重置</button>
                        <button class="self-inspection__btn self-inspection__btn--upload" onclick="showUploadDialog('${stage}', '${role}')">上传附件</button>
                        <span class="self-inspection__completed-tag">已完成</span>
                    `;
                } else {
                    newActionBar.innerHTML = `
                        <button class="self-inspection__btn self-inspection__btn--reset" onclick="resetInspection('${stage}', '${role}')">重置</button>
                        <button class="self-inspection__btn self-inspection__btn--upload" onclick="showUploadDialog('${stage}', '${role}')">上传附件</button>
                        <button class="self-inspection__btn self-inspection__btn--submit" onclick="submitInspection('${stage}', '${role}')">提交${role === window.ROLES.SELF ? '自检' : (role === window.ROLES.FIRST ? '首检' : 'IPQC')}</button>
                    `;
                }

                // 添加到父容器
                parentContainer.appendChild(newActionBar);
                console.log('已创建操作按钮容器');
            } else {
                console.log('已存在操作按钮容器，无需创建');
            }
        } else {
            console.error(`找不到父容器: ${stage}-${role}`);
        }
    } else {
        console.log('找到操作按钮容器，检查是否需要更新');

        // 直接使用阶段和角色名称
        // 检查是否已提交
        const isSubmitted = inspectionData.pageState.inspectionSubmissions[stage]?.[role]?.isSubmitted || false;

        if (isSubmitted) {
            actionBar.innerHTML = `
                <button class="self-inspection__btn self-inspection__btn--reset self-inspection__btn--disabled" disabled>重置</button>
                <button class="self-inspection__btn self-inspection__btn--upload" onclick="showUploadDialog('${stage}', '${role}')">上传附件</button>
                <span class="self-inspection__completed-tag">已完成</span>
            `;
        } else if (!actionBar.querySelector('.self-inspection__btn--submit')) {
            // 如果没有提交按钮，添加它
            actionBar.innerHTML = `
                <button class="self-inspection__btn self-inspection__btn--reset" onclick="resetInspection('${stage}', '${role}')">重置</button>
                <button class="self-inspection__btn self-inspection__btn--upload" onclick="showUploadDialog('${stage}', '${role}')">上传附件</button>
                <button class="self-inspection__btn self-inspection__btn--submit" onclick="submitInspection('${stage}', '${role}')">提交${role === window.ROLES.SELF ? '自检' : (role === window.ROLES.FIRST ? '首检' : 'IPQC')}</button>
            `;
        }
    }

    // 渲染附件列表
    renderAttachmentFiles(stage, role);
}

// 获取阶段显示名称
function getStageDisplayName(stage) {
    const stageNames = {
        [window.STAGES.ASSEMBLY]: '组装前阶段',
        [window.STAGES.TEST]: '测试前阶段',
        [window.STAGES.PACKAGING]: '包装前阶段'
    };
    return stageNames[stage] || stage;
}

// 获取角色显示名称
function getRoleDisplayName(role) {
    const roleNames = {
        [window.ROLES.FIRST]: '首检',
        [window.ROLES.SELF]: '自检',
        [window.ROLES.IPQC]: 'IPQC'
    };
    return roleNames[role] || role;
}

// 添加SN号扫描样式
function addScanSNStyles() {
    const style = document.createElement('style');
    style.textContent = `
        /* SN号扫描界面样式 */
        .scan-sn-container {
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .scan-sn-header {
            margin-bottom: 20px;
        }

        .scan-sn-header h3 {
            font-size: 20px;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .scan-sn-info {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            font-size: 14px;
            color: #6c757d;
        }

        .scan-sn-info span {
            background-color: #f8f9fa;
            padding: 5px 10px;
            border-radius: 4px;
            border-left: 3px solid #4CAF50;
        }

        .scan-input-group {
            display: flex;
            margin-bottom: 20px;
        }

        .scan-input-group input {
            flex: 1;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 6px 0 0 6px;
            font-size: 16px;
            transition: border-color 0.2s;
        }

        .scan-input-group input:focus {
            outline: none;
            border-color: #4CAF50;
        }

        .scan-confirm-btn {
            padding: 12px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 0 6px 6px 0;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .scan-confirm-btn:hover {
            background-color: #43a047;
        }

        .scanned-products {
            margin-bottom: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #e9ecef;
        }

        .scanned-products-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .scanned-products-header h4 {
            font-size: 16px;
            color: #2c3e50;
            margin: 0;
        }

        .clear-all-btn {
            padding: 5px 10px;
            background-color: #f8f9fa;
            color: #dc3545;
            border: 1px solid #dc3545;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }

        .clear-all-btn:hover {
            background-color: #dc3545;
            color: white;
        }

        .scanned-product-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .no-products {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 10px;
        }

        .scanned-product-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background-color: white;
            border-radius: 6px;
            margin-bottom: 8px;
            border-left: 3px solid #4CAF50;
        }

        .product-sn {
            font-weight: 500;
            color: #2c3e50;
        }

        .remove-product-btn {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f8f9fa;
            color: #dc3545;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.2s;
        }

        .remove-product-btn:hover {
            background-color: #dc3545;
            color: white;
        }

        .scan-actions {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .btn-primary {
            padding: 12px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.2s;
            text-align: center;
        }

        .btn-primary:hover:not(:disabled) {
            background-color: #43a047;
        }

        .btn-primary:disabled {
            background-color: #a5d6a7;
            cursor: not-allowed;
        }

        .self-inspection-note, .sampling-note {
            padding: 10px 15px;
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            color: #856404;
            border-radius: 4px;
            font-size: 14px;
        }

        .self-inspection-note i, .sampling-note i {
            margin-right: 8px;
        }
    `;
    document.head.appendChild(style);
}

// 显示通知
function showNotification(type, message, details = '') {
    console.log(`[${type}] ${message}`, details);

    // 如果存在SweetAlert，使用它
    if (typeof SweetAlert !== 'undefined') {
        if (type === 'success') {
            SweetAlert.success(message, details);
        } else if (type === 'error') {
            SweetAlert.error(message, details);
        } else if (type === 'warning') {
            SweetAlert.warning(message, details);
        } else {
            SweetAlert.info(message, details);
        }
    }
    // 否则使用alert
    else {
        alert(`${type.toUpperCase()}: ${message}\n${details}`);
    }
}

// 渲染附件列表
function renderAttachmentFiles(stage, role) {
    console.log('渲染附件列表', stage, role);

    // 检查是否存在renderAttachmentFiles函数
    if (typeof window.renderAttachmentFiles === 'function' && window.renderAttachmentFiles !== renderAttachmentFiles) {
        // 如果存在全局的renderAttachmentFiles函数，调用它
        window.renderAttachmentFiles(stage, role);
        return;
    }

    // 使用统一的容器ID格式
    const attachmentContainerSelector = `#${stage}-${role}-attachments`;
    const attachmentContainer = document.querySelector(attachmentContainerSelector);

    if (!attachmentContainer) {
        console.warn(`找不到附件容器: ${attachmentContainerSelector}`);

        // 查找父容器
        const parentContainer = document.getElementById(`${stage}-${role}`);

        if (parentContainer) {
            console.log('找到父容器，尝试创建附件容器');

            // 检查是否已经有附件容器
            let existingAttachmentContainer = parentContainer.querySelector('.self-inspection__attachments');

            if (!existingAttachmentContainer) {
                // 创建附件容器
                const newAttachmentContainer = document.createElement('div');
                newAttachmentContainer.className = 'self-inspection__attachments';
                newAttachmentContainer.id = `${stage}-${role}-attachments`;

                // 直接使用阶段和角色名称

                // 获取附件列表
                const attachments = inspectionData.pageState.attachments[stage]?.[role] || [];

                if (attachments.length === 0) {
                    newAttachmentContainer.innerHTML = '<p>暂无附件</p>';
                } else {
                    let attachmentHtml = '<div class="self-inspection__attachment-list">';

                    attachments.forEach(attachment => {
                        attachmentHtml += `
                            <div class="self-inspection__attachment-item">
                                <div class="self-inspection__attachment-icon">${getFileIcon(attachment.type)}</div>
                                <div class="self-inspection__attachment-info">
                                    <div class="self-inspection__attachment-name">${attachment.name}</div>
                                    <div class="self-inspection__attachment-size">${attachment.size}</div>
                                </div>
                            </div>
                        `;
                    });

                    attachmentHtml += '</div>';
                    newAttachmentContainer.innerHTML = attachmentHtml;
                }

                // 添加到操作按钮之前
                const actionBar = parentContainer.querySelector('.self-inspection__action-bar');
                if (actionBar) {
                    parentContainer.insertBefore(newAttachmentContainer, actionBar);
                } else {
                    parentContainer.appendChild(newAttachmentContainer);
                }

                console.log('已创建附件容器');
            } else {
                console.log('已存在附件容器，无需创建');
            }
        } else {
            console.error(`找不到父容器: ${stage}-${role}`);
        }
    } else {
        console.log('找到附件容器，检查是否需要更新');

        // 直接使用阶段和角色名称

        // 获取附件列表
        const attachments = inspectionData.pageState.attachments[stage]?.[role] || [];

        if (attachments.length === 0) {
            attachmentContainer.innerHTML = '<p>暂无附件</p>';
        } else {
            let attachmentHtml = '<div class="self-inspection__attachment-list">';

            attachments.forEach(attachment => {
                attachmentHtml += `
                    <div class="self-inspection__attachment-item">
                        <div class="self-inspection__attachment-icon">${getFileIcon(attachment.type)}</div>
                        <div class="self-inspection__attachment-info">
                            <div class="self-inspection__attachment-name">${attachment.name}</div>
                            <div class="self-inspection__attachment-size">${attachment.size}</div>
                        </div>
                    </div>
                `;
            });

            attachmentHtml += '</div>';
            attachmentContainer.innerHTML = attachmentHtml;
        }
    }
}

// 获取文件图标
function getFileIcon(fileType) {
    if (typeof window.getFileIcon === 'function' && window.getFileIcon !== getFileIcon) {
        return window.getFileIcon(fileType);
    }

    switch (fileType) {
        case 'image':
            return '<i class="fas fa-file-image"></i>';
        case 'pdf':
            return '<i class="fas fa-file-pdf"></i>';
        case 'word':
            return '<i class="fas fa-file-word"></i>';
        case 'excel':
            return '<i class="fas fa-file-excel"></i>';
        case 'powerpoint':
            return '<i class="fas fa-file-powerpoint"></i>';
        case 'text':
            return '<i class="fas fa-file-alt"></i>';
        default:
            return '<i class="fas fa-file"></i>';
    }
}

// 获取当前登录用户信息
function getCurrentUserInfo() {
    // 如果已经有全局用户信息，直接使用
    if (window.currentLoggedInUser) {
        return;
    }

    // 尝试从多个可能的API端点获取当前用户信息
    fetch('/api/user/info')
        .then(response => {
            if (!response.ok) {
                // 如果第一个API失败，尝试其他API
                return fetch('/api/work-order/current-user');
            }
            return response.json();
        })
        .then(data => {
            if (data && data.success && data.username) {
                // 保存当前用户名到全局变量，以便在其他地方使用
                window.currentLoggedInUser = data.username;
                return;
            }

            // 如果第一个API失败，尝试其他API
            return fetch('/api/work-order/current-user');
        })
        .then(response => {
            if (response && response.json) {
                return response.json();
            }
            return null;
        })
        .then(data => {
            if (data && data.success && data.username && !window.currentLoggedInUser) {
                window.currentLoggedInUser = data.username;
            }
        })
        .catch(error => {
            console.warn('获取当前用户信息失败:', error);
        });
}

// 初始化
(function() {
    // 获取当前用户信息
    getCurrentUserInfo();
})();

// 将函数暴露到全局作用域
window.showScanSerialNumberUI = showScanSerialNumberUI;
window.processScanedSN = processScanedSN;
window.removeScannedProduct = removeScannedProduct;
window.clearScannedProducts = clearScannedProducts;
window.startInspection = startInspection;
window.renderActionButtons = renderActionButtons;
window.renderAttachmentFiles = renderAttachmentFiles;
window.getCurrentUserInfo = getCurrentUserInfo;

// 新增：验证SN号与工单的订货号是否匹配
function validateSnForOrderNumber(serialNo, productCode) {
    if (!productCode) {
        showNotification('error', '无法验证SN号', '未能获取到当前工单的产品编码');
        return Promise.resolve(false);
    }

    return fetch(`/api/products/validate-sn-order?sn=${encodeURIComponent(serialNo)}&productCode=${encodeURIComponent(productCode)}`)
        .then(response => response.json())
        .then(result => {
            if (!result.success) {
                showNotification('error', 'SN号与订货号不匹配', result.message || '请检查SN号是否正确');
                return false;
            }
            return true;
        })
        .catch(error => {
            console.error('验证SN订货号失败:', error);
            showNotification('error', '验证SN号失败', '网络请求错误，请稍后重试');
            return false;
        });
}
