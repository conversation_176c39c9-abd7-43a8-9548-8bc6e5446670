# 产品重复检查功能 - 完整部署指南

## 功能概述

基于实际数据库表结构实现的产品重复检查功能，防止在同一工单的同一阶段同一角色中重复录入已完成最终检验的产品。

## 核心查询逻辑

```sql
-- 检查产品是否已被最终检验过
SELECT pis.id, pis.product_id, pis.inspector, pis.inspection_time
FROM product_inspection_status pis
INNER JOIN products p ON pis.product_id = p.id
WHERE pis.work_order_id = ? 
    AND p.serial_no = ? 
    AND pis.stage = ? 
    AND pis.inspector_role = ?
    AND pis.submission_type = 'final'  -- 关键：只检查最终提交
LIMIT 1
```

## 部署步骤

### 第一步：数据库优化
```bash
# 1. 执行索引优化脚本
mysql -u root -p kmlc_plc < optimize_duplicate_check_indexes.sql

# 2. 验证索引创建成功
mysql -u root -p -e "
USE kmlc_plc;
SHOW INDEX FROM product_inspection_status WHERE Key_name='idx_duplicate_check';
SHOW INDEX FROM products WHERE Key_name='idx_products_work_order_serial';
"
```

### 第二步：后端API部署
```bash
# 1. 安装依赖
pip install flask pymysql

# 2. 修改数据库配置
# 编辑 quality_inspection_duplicate_check.py 中的数据库连接参数:
# MYSQL_HOST, MYSQL_USER, MYSQL_PASSWORD 等

# 3. 测试运行
python quality_inspection_duplicate_check.py

# 4. 验证API可用性
curl "http://localhost:5000/api/health"
```

### 第三步：前端集成
前端代码已在 `ScanSN.js` 中完成，无需额外部署。

### 第四步：功能测试
```bash
# 测试API调用 (请替换为实际的测试数据)
curl "http://localhost:5000/api/quality-inspection/check-product-duplicate?work_order_id=1&serial_no=TEST001&stage=assembly&inspector_role=first"

# 预期响应:
# {"success": true, "already_inspected": false, "message": "产品未在当前阶段完成最终检验"}
```

## 生产环境配置

### 数据库配置
```python
# quality_inspection_duplicate_check.py 中的生产配置
app.config.update({
    'MYSQL_HOST': 'your_production_host',
    'MYSQL_PORT': 3306,
    'MYSQL_USER': 'quality_inspection_user',  # 建议创建专用用户
    'MYSQL_PASSWORD': 'secure_password',
    'MYSQL_DB': 'kmlc_plc',
    'DEBUG': False  # 生产环境必须设置为False
})
```

### 数据库用户权限
```sql
-- 为API创建专用数据库用户（推荐）
CREATE USER 'quality_inspection_api'@'%' IDENTIFIED BY 'secure_password';

-- 只授予必要的权限
GRANT SELECT ON kmlc_plc.product_inspection_status TO 'quality_inspection_api'@'%';
GRANT SELECT ON kmlc_plc.products TO 'quality_inspection_api'@'%';
GRANT SELECT ON kmlc_plc.quality_inspection_work_orders TO 'quality_inspection_api'@'%';

-- 授予索引查询权限
GRANT SELECT ON information_schema.statistics TO 'quality_inspection_api'@'%';

FLUSH PRIVILEGES;
```

### WSGI部署（推荐）
```bash
# 1. 安装Gunicorn
pip install gunicorn

# 2. 创建WSGI配置文件 wsgi.py
cat > wsgi.py << 'EOF'
from quality_inspection_duplicate_check import create_app
application = create_app()

if __name__ == "__main__":
    application.run()
EOF

# 3. 使用Gunicorn运行
gunicorn --bind 0.0.0.0:5000 --workers 4 --timeout 30 wsgi:application
```

## 监控和维护

### 性能监控
```sql
-- 1. 监控查询性能
SELECT 
    COUNT(*) as total_queries,
    AVG(TIMER_WAIT/1000000000) as avg_response_time_seconds
FROM performance_schema.events_statements_history_long
WHERE SQL_TEXT LIKE '%product_inspection_status%'
    AND SQL_TEXT LIKE '%submission_type = \'final\'%';

-- 2. 监控索引使用情况
SELECT 
    INDEX_NAME,
    COUNT_READ,
    COUNT_FETCH,
    SUM_TIMER_READ/1000000000 as total_read_time_seconds
FROM performance_schema.table_io_waits_summary_by_index_usage
WHERE OBJECT_SCHEMA = 'kmlc_plc'
    AND OBJECT_NAME = 'product_inspection_status'
    AND INDEX_NAME = 'idx_duplicate_check';
```

### 日志监控
```bash
# 监控API错误日志
tail -f quality_inspection.log | grep -E "(ERROR|WARNING)"

# 监控MySQL慢查询
tail -f /var/log/mysql/mysql-slow.log | grep "product_inspection_status"
```

## 故障排除

### 常见问题

1. **API返回500错误**
   ```bash
   # 检查数据库连接
   mysql -u quality_inspection_api -p -h your_host -e "SELECT 1"
   
   # 检查表是否存在
   mysql -u quality_inspection_api -p kmlc_plc -e "SHOW TABLES"
   ```

2. **查询速度慢**
   ```sql
   -- 检查索引是否被使用
   EXPLAIN SELECT pis.id
   FROM product_inspection_status pis
   INNER JOIN products p ON pis.product_id = p.id
   WHERE pis.work_order_id = 1 
       AND p.serial_no = 'TEST001' 
       AND pis.stage = 'assembly' 
       AND pis.inspector_role = 'first'
       AND pis.submission_type = 'final';
   ```

3. **前端超时**
   ```javascript
   // 检查前端控制台是否有错误信息
   // 查看网络请求是否正常发送
   ```

### 调试模式
```python
# 临时启用调试模式查看详细信息
app.config['DEBUG'] = True
```

## 回滚方案

如果需要紧急回滚：

1. **禁用API**：停止后端服务
2. **前端回滚**：删除ScanSN.js中的重复检查代码
3. **数据库回滚**：删除新创建的索引（可选）

```sql
-- 删除新创建的索引（如果需要）
DROP INDEX idx_duplicate_check ON product_inspection_status;
DROP INDEX idx_products_work_order_serial ON products;
```

## 成功部署验证

部署完成后，进行以下验证：

1. ✅ API健康检查通过
2. ✅ 数据库索引创建成功
3. ✅ 前端能正常调用API
4. ✅ 重复检查功能正常工作
5. ✅ 现有功能不受影响

## 联系方式

如有问题，请检查：
1. 日志文件 `quality_inspection.log`
2. MySQL错误日志
3. 浏览器开发者工具控制台

---

**重要提醒**: 
- 生产环境部署前请务必在测试环境验证
- 确保数据库备份已完成
- 建议在业务低峰期进行部署