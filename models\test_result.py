from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, Index
from sqlalchemy.ext.declarative import declared_attr
from database.db_manager import Base
from datetime import datetime

class TestResultBase(Base):
    """测试结果基类"""
    __abstract__ = True
    
    id = Column(Integer, primary_key=True)
    work_order = Column(String(50), index=True, comment='工单号')
    pro_sn = Column(String(50), index=True, comment='产品序列号')
    pro_code = Column(String(50), index=True, comment='产品编码')
    pro_model = Column(String(50), comment='产品型号')
    pro_status = Column(Integer, comment='产品状态: 1=新品, 2=维修, 3=返工')
    tester = Column(String(50), comment='测试人员')
    test_time = Column(DateTime, default=datetime.now, index=True, comment='测试时间')
    test_status = Column(String(10), comment='测试结果')
    pro_batch = Column(String(50), comment='产品批次')
    rework = Column(Integer, default=0, comment='返工次数')
    maintenance = Column(Integer, default=0, comment='维修次数')
    remarks = Column(Text, comment='备注')
    work_qty = Column(Integer, comment='生产量')
    comparison_result = Column(String(10), comment='比对结果')
    comparison_time = Column(DateTime, comment='比对时间')
    comparison_user = Column(String(50), comment='比对人员')
    
    @property
    def status_display(self):
        status_map = {1: '新品', 2: '维修', 3: '返工'}
        return status_map.get(self.pro_status, '未知')

class CPUTest(TestResultBase):
    """CPU控制器测试结果"""
    __tablename__ = 'cpu_table'
    
    # CPU特有字段
    device_name = Column(String(50), comment='设备名称')
    serial = Column(String(50), comment='序列号')
    sw_version = Column(String(50), comment='软件版本')
    build_date = Column(String(50), comment='构建日期')
    back_ver = Column(String(50), comment='背板版本')
    high_speed_io_version = Column(String(50), comment='高速脉冲版本')
    
    # 测试项目字段
    backplane = Column(Integer, comment='背板通信')
    body_io = Column(Integer, comment='Body I/O')
    led_tube = Column(Integer, comment='LED数码管')
    led_bulb = Column(Integer, comment='LED灯珠')
    net_port = Column(Integer, comment='网口')
    rs485_1 = Column(Integer, comment='RS485_1')
    rs485_2 = Column(Integer, comment='RS485_2')
    rs232 = Column(Integer, comment='RS232')
    canbus = Column(Integer, comment='CANbus')
    ethercat = Column(Integer, comment='EtherCAT')
    usb_drive = Column(Integer, comment='USB接口')
    sd_slot = Column(Integer, comment='SD卡槽')
    debug_port = Column(Integer, comment='调试串口')
    dip_switch = Column(Integer, comment='拨码开关')
    reset_btn = Column(Integer, comment='复位按钮')
    
    # CPU表的索引
    __table_args__ = (
        Index('idx_cpu_work_order_test_time', 'work_order', 'test_time'),
        Index('idx_cpu_pro_sn_test_time', 'pro_sn', 'test_time'),
        Index('idx_cpu_test_status_test_time', 'test_status', 'test_time'),
    )

class CouplerTest(TestResultBase):
    """耦合器和IO模块测试结果"""
    __tablename__ = 'coupler_table'
    
    # 耦合器特有字段
    product_type = Column(String(20), comment='产品类型')
    couplersw_version = Column(String(50), comment='软件版本')
    coupler_date = Column(String(50), comment='构建日期')
    
    # 测试项目字段
    backplane = Column(Integer, comment='背板通信')
    body_io = Column(Integer, comment='Body I/O')
    led_tube = Column(Integer, comment='LED数码管')
    led_bulb = Column(Integer, comment='LED灯珠')
    net_port = Column(Integer, comment='网口')
    
    # 耦合器表的索引
    __table_args__ = (
        Index('idx_coupler_work_order_test_time', 'work_order', 'test_time'),
        Index('idx_coupler_pro_sn_test_time', 'pro_sn', 'test_time'),
        Index('idx_coupler_test_status_test_time', 'test_status', 'test_time'),
    )
    
    @property
    def display_type(self):
        type_map = {
            'io_module': 'IO模块',
            'coupler_module': '耦合器'
        }
        return type_map.get(self.product_type, self.product_type)

class FaultEntry(Base):
    """故障记录表 - CPU控制器测试故障信息"""
    __tablename__ = 'faultEntry_table'
    
    # 基本字段（完全匹配实际表结构）
    id = Column(Integer, primary_key=True, autoincrement=True)
    tester = Column(String(20), nullable=False, comment='测试人员')
    work_order = Column(String(60), nullable=False, index=True, comment='工单号')
    work_qty = Column(Integer, nullable=False, comment='生产量')
    pro_model = Column(String(60), nullable=False, comment='产品型号')
    pro_code = Column(String(60), nullable=False, index=True, comment='产品编码')
    pro_status = Column(Integer, nullable=False, comment='产品状态: 1=新品, 2=维修, 3=返工')
    pro_sn = Column(String(60), nullable=True, default='N/A', comment='产品序列号')
    pro_batch = Column(String(60), nullable=True, default='N/A', comment='产品批次')
    remarks = Column(String(150), nullable=True, default='N/A', comment='备注')
    rework = Column(Integer, nullable=True, default=0, comment='返工次数')
    maintenance = Column(Integer, nullable=True, default=0, comment='维修次数')
    
    # 故障类型字段 (tinyint, 0=无故障, 2=有故障)
    power_err = Column(Integer, nullable=True, default=0, comment='电源故障')
    burn_err = Column(Integer, nullable=True, default=0, comment='烧录故障')
    backplane_err = Column(Integer, nullable=True, default=0, comment='背板通信故障')
    body_io_err = Column(Integer, nullable=True, default=0, comment='Body I/O故障')
    led_tube_err = Column(Integer, nullable=True, default=0, comment='LED数码管故障')
    led_bulb_err = Column(Integer, nullable=True, default=0, comment='LED灯珠故障')
    net_port_err = Column(Integer, nullable=True, default=0, comment='网口故障')
    rs485_1_err = Column(Integer, nullable=True, default=0, comment='RS485_1故障')
    rs485_2_err = Column(Integer, nullable=True, default=0, comment='RS485_2故障')
    rs232_err = Column(Integer, nullable=True, default=0, comment='RS232故障')
    canbus_err = Column(Integer, nullable=True, default=0, comment='CANbus故障')
    ethercat_err = Column(Integer, nullable=True, default=0, comment='EtherCAT故障')
    usb_drive_err = Column(Integer, nullable=True, default=0, comment='USB接口故障')
    sd_slot_err = Column(Integer, nullable=True, default=0, comment='SD卡槽故障')
    debug_port_err = Column(Integer, nullable=True, default=0, comment='调试串口故障')
    dip_switch_err = Column(Integer, nullable=True, default=0, comment='拨码开关故障')
    reset_btn_err = Column(Integer, nullable=True, default=0, comment='复位按钮故障')
    other_err = Column(String(255), nullable=True, default='N/A', comment='其他故障')
    
    # 时间字段（实际表中存在）
    test_time = Column(DateTime, nullable=False, default=datetime.now, comment='测试时间')
    
    # 故障表的索引
    __table_args__ = (
        Index('idx_fault_work_order_test_time', 'work_order', 'test_time'),
        Index('idx_fault_pro_sn_test_time', 'pro_sn', 'test_time'),
        Index('idx_fault_tester_test_time', 'tester', 'test_time'),
    )
    
    @property
    def fault_count(self):
        """计算故障总数"""
        fault_fields = [
            self.burn_err, self.power_err, self.backplane_err, self.body_io_err,
            self.led_tube_err, self.led_bulb_err, self.net_port_err, 
            self.rs485_1_err, self.rs485_2_err, self.rs232_err,
            self.canbus_err, self.ethercat_err, self.usb_drive_err,
            self.sd_slot_err, self.debug_port_err, self.dip_switch_err,
            self.reset_btn_err
        ]
        return sum(1 for field in fault_fields if field == 2)
    
    @property
    def fault_list(self):
        """获取故障项目列表"""
        fault_map = {
            'burn_err': '烧录故障',
            'power_err': '电源故障', 
            'backplane_err': '背板通信故障',
            'body_io_err': 'Body I/O故障',
            'led_tube_err': 'LED数码管故障',
            'led_bulb_err': 'LED灯珠故障',
            'net_port_err': '网口故障',
            'rs485_1_err': 'RS485_1故障',
            'rs485_2_err': 'RS485_2故障',
            'rs232_err': 'RS232故障',
            'canbus_err': 'CANbus故障',
            'ethercat_err': 'EtherCAT故障',
            'usb_drive_err': 'USB接口故障',
            'sd_slot_err': 'SD卡槽故障',
            'debug_port_err': '调试串口故障',
            'dip_switch_err': '拨码开关故障',
            'reset_btn_err': '复位按钮故障'
        }
        
        faults = []
        for field_name, display_name in fault_map.items():
            if getattr(self, field_name) == 2:
                faults.append(display_name)
                
        return faults 