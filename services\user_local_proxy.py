from flask import Flask, jsonify, request
import requests
from flask_cors import CORS
import logging
from requests.auth import HTTPBasicAuth
import os
from werkzeug.utils import secure_filename
import time
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry
from functools import wraps
from collections import deque

app = Flask(__name__)
CORS(app)  # 启用CORS支持

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 代理服务器配置
PROXY_PORT = 5000

# PLC认证配置
PLC_USERNAME = 'admin'
PLC_PASSWORD = 'admin'

# 添加文件上传配置
UPLOAD_FOLDER = 'temp'
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

# 配置重试策略
retry_strategy = Retry(
    total=3,  # 最大重试次数
    backoff_factor=0.5,  # 重试间隔
    status_forcelist=[500, 502, 503, 504]  # 需要重试的HTTP状态码
)

# 创建会话对象
def create_session():
    session = requests.Session()
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    session.auth = (PLC_USERNAME, PLC_PASSWORD)
    return session

# 添加请求速率限制
class RateLimiter:
    def __init__(self, max_requests, time_window):
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = deque()

    def __call__(self, func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            now = time.time()
            
            # 移除过期的请求记录
            while self.requests and now - self.requests[0] > self.time_window:
                self.requests.popleft()
            
            # 检查是否超过速率限制
            if len(self.requests) >= self.max_requests:
                time_to_wait = self.requests[0] + self.time_window - now
                if time_to_wait > 0:
                    time.sleep(time_to_wait)
            
            self.requests.append(now)
            return func(*args, **kwargs)
        return wrapper

# 使用速率限制装饰器
rate_limiter = RateLimiter(max_requests=10, time_window=1)  # 每秒最多10个请求

@app.route('/proxy/device-info', methods=['GET'])
@rate_limiter
def proxy_device_info():
    session = create_session()
    try:
        plc_ip = request.args.get('ip')
        if not plc_ip:
            return jsonify({'success': False, 'message': 'IP地址不能为空'})

        # 构建PLC设备的URL
        plc_url = f"http://{plc_ip}:8090/cgi-bin/devinfo?key=device"
        
        logger.info(f"Proxying request to: {plc_url}")
        
        # 使用HTTPBasicAuth进行认证
        response = session.get(
            plc_url, 
            timeout=5
        )
        
        logger.info(f"Response status code: {response.status_code}")
        
        if response.status_code == 401:
            logger.error("Authentication failed")
            return jsonify({
                'success': False,
                'message': 'PLC认证失败，请检查用户名和密码'
            })
        
        if response.status_code == 200:
            data = response.json()
            # 处理响应数据格式，参考device.py中的格式
            if data.get("error") is False and "data" in data:
                inner_data = next(iter(data["data"].values()))
                logger.info(f"Processed response data: {inner_data}")
                return jsonify({
                    'success': True,
                    'data': inner_data
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '设备返回数据格式错误'
                })
        else:
            logger.error(f"PLC returned error status: {response.status_code}")
            return jsonify({
                'success': False,
                'message': f'PLC返回错误状态码: {response.status_code}'
            })

    except requests.exceptions.Timeout:
        logger.error("Request timeout")
        return jsonify({
            'success': False,
            'message': 'PLC连接超时'
        })
    except Exception as e:
        logger.error(f"Error in proxy_device_info: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'代理服务器错误: {str(e)}'
        })
    finally:
        session.close()

@app.route('/proxy/network-info/<interface>', methods=['GET'])
def proxy_network_info(interface):
    try:
        plc_ip = request.args.get('ip')
        if not plc_ip:
            return jsonify({'success': False, 'message': 'IP地址不能为空'})

        plc_url = f"http://{plc_ip}:8090/cgi-bin/netinfo?key={interface}"
        logger.info(f"Proxying network request to: {plc_url}")
        
        response = requests.get(
            plc_url, 
            auth=HTTPBasicAuth(PLC_USERNAME, PLC_PASSWORD),
            timeout=5
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("error") is False and "data" in data:
                # 获取实际的网络信息数据
                inner_data = next(iter(data["data"].values()))
                # 获取最后一个键值对（MAC地址）
                last_key = list(inner_data)[-1]
                mac_address = inner_data[last_key]
                
                return jsonify({
                    'success': True,
                    'data': {
                        'mac': mac_address,
                        'third_from_last': mac_address.split(':')[3] if ':' in mac_address else 'N/A'
                    }
                })
        
        return jsonify({
            'success': False,
            'message': f'获取网络信息失败: {response.status_code}'
        })

    except Exception as e:
        logger.error(f"Error in proxy_network_info: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'代理服务器错误: {str(e)}'
        })

@app.route('/proxy/memory-info', methods=['GET'])
def proxy_memory_info():
    try:
        plc_ip = request.args.get('ip')
        if not plc_ip:
            return jsonify({'success': False, 'message': 'IP地址不能为空'})

        # 构建内存信息请求URL
        plc_url = f"http://{plc_ip}:8090/cgi-bin/memory"
        params = {
            'key': 'memory',
            'type': '2',
            'startAddress': '0',
            'count': '256',
            'startIndex': '0',
            'allcount': '256'
        }
        
        logger.info(f"Proxying memory request to: {plc_url}")
        
        # 使用HTTPBasicAuth进行认证
        response = requests.get(
            plc_url, 
            params=params,
            auth=HTTPBasicAuth(PLC_USERNAME, PLC_PASSWORD),
            timeout=5
        )
        
        logger.info(f"Memory response status code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if not data.get('error'):
                memory_values = data['data']['memory'][0]['value'][:19]
                logger.info(f"Memory values: {memory_values}")
                return jsonify({
                    'success': True,
                    'data': memory_values
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '获取内存数据失败'
                })
        else:
            return jsonify({
                'success': False,
                'message': f'PLC返回错误状态码: {response.status_code}'
            })

    except Exception as e:
        logger.error(f"Error in proxy_memory_info: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'代理服务器错误: {str(e)}'
        })

@app.route('/proxy/reset-device', methods=['POST'])
def proxy_reset_device():
    try:
        data = request.get_json()
        plc_ip = data.get('ip')
        if not plc_ip:
            return jsonify({'success': False, 'message': 'IP地址不能为空'})

        # 构建PLC设备的重置URL
        plc_url = f"http://{plc_ip}:8090/cgi-bin/reset?key=reset"
        
        logger.info(f"Proxying reset request to: {plc_url}")
        
        # 使用HTTPBasicAuth进行认证
        response = requests.get(
            plc_url, 
            auth=HTTPBasicAuth(PLC_USERNAME, PLC_PASSWORD),
            timeout=5
        )
        
        logger.info(f"Reset response status code: {response.status_code}")
        
        if response.status_code == 200:
            return jsonify({
                'success': True,
                'message': '恢复出厂设置成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': f'设备返回错误状态码: {response.status_code}'
            })

    except requests.exceptions.Timeout:
        logger.error("Reset request timeout")
        return jsonify({
            'success': False,
            'message': '设备连接超时'
        })
    except Exception as e:
        logger.error(f"Error in proxy_reset_device: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'代理服务器错误: {str(e)}'
        })

@app.route('/proxy/upload-program', methods=['POST'])
def proxy_upload_program():
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'message': '没有文件被上传'})
        
        file = request.files['file']
        plc_ip = request.form.get('ip')
        
        if not plc_ip:
            return jsonify({'success': False, 'message': 'IP地址不能为空'})
        
        if file.filename == '':
            return jsonify({'success': False, 'message': '没有选择文件'})
        
        if not file.filename.endswith('.zip'):
            return jsonify({'success': False, 'message': '只支持上传zip文件'})

        # 保存文件到临时目录
        filename = secure_filename(file.filename)
        temp_path = os.path.join(UPLOAD_FOLDER, filename)
        file.save(temp_path)

        try:
            # 构建PLC设备的上传URL
            plc_url = f"http://{plc_ip}:8090/cgi-bin/upload"
            
            # 创建自定义boundary
            boundary = '----WebKitFormBoundary' + ''.join(['1' for _ in range(16)])
            
            headers = {
                'Content-Type': f'multipart/form-data; boundary={boundary}',
            }
            
            # 按照设备要求构建表单数据
            with open(temp_path, 'rb') as f:
                form = (
                    f'--{boundary}\r\n'
                    f'Content-Disposition: form-data; name="file"; filename="{filename}"\r\n'
                    f'Content-Type: application/x-zip-compressed\r\n\r\n'
                )
                form_end = f'\r\n--{boundary}--\r\n'
                
                body = form.encode('utf-8') + f.read() + form_end.encode('utf-8')
            
            logger.info(f"Uploading file to PLC: {plc_url}")
            
            # 发送请求
            response = requests.post(
                plc_url,
                data=body,
                headers=headers,
                auth=HTTPBasicAuth(PLC_USERNAME, PLC_PASSWORD),
                timeout=30
            )
            
            logger.info(f"Upload response status: {response.status_code}")
            logger.info(f"Upload response content: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                if not result.get('error'):
                    return jsonify({
                        'success': True,
                        'message': '文件上传成功'
                    })
            
            return jsonify({
                'success': False,
                'message': f'文件上传失败: {response.text}'
            })

        finally:
            # 确保清理临时文件
            try:
                os.remove(temp_path)
            except:
                pass

    except Exception as e:
        logger.error(f"Error in proxy_upload_program: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'代理服务器错误: {str(e)}'
        })

@app.route('/proxy/update-program', methods=['POST'])
def proxy_update_program():
    try:
        data = request.get_json()
        plc_ip = data.get('ip')
        filename = data.get('filename')
        
        if not plc_ip:
            return jsonify({'success': False, 'message': 'IP地址不能为空'})
        
        if not filename:
            return jsonify({'success': False, 'message': '文件名不能为空'})

        # 构建PLC设备的更新URL
        plc_url = f"http://{plc_ip}:8090/cgi-bin/system?key=updateapp"
        
        # 设置与设备要求一致的请求头
        headers = {
            'Content-Type': 'application/json;charset=UTF-8',
            'Origin': f'http://{plc_ip}:8090',
            'Referer': f'http://{plc_ip}:8090/'
        }
        
        update_data = {"package": filename}
        
        logger.info(f"Updating program on PLC: {plc_url}")
        logger.info(f"Update headers: {headers}")
        logger.info(f"Update data: {update_data}")
        
        # 发送更新请求
        response = requests.post(
            plc_url,
            json=update_data,
            headers=headers,
            auth=HTTPBasicAuth(PLC_USERNAME, PLC_PASSWORD),
            timeout=30
        )
        
        logger.info(f"Update response status: {response.status_code}")
        logger.info(f"Update response content: {response.text}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                if not result.get('error', True):  # 注意这里的默认值改为 True
                    time.sleep(2)  # 等待设备开始更新
                    return jsonify({
                        'success': True,
                        'message': '程序更新指令已发送，设备正在更新'
                    })
                else:
                    error_msg = result.get('message', '未知错误')
                    logger.error(f"Update failed with error: {error_msg}")
                    return jsonify({
                        'success': False,
                        'message': f'程序更新失败: {error_msg}'
                    })
            except ValueError as e:
                logger.error(f"Failed to parse JSON response: {e}")
                return jsonify({
                    'success': False,
                    'message': '程序更新响应格式错误'
                })
        
        return jsonify({
            'success': False,
            'message': f'程序更新失败，状态码: {response.status_code}'
        })

    except requests.exceptions.Timeout:
        logger.error("Update request timeout")
        return jsonify({
            'success': False,
            'message': '更新请求超时'
        })
    except Exception as e:
        logger.error(f"Error in proxy_update_program: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'代理服务器错误: {str(e)}'
        })

@app.route('/proxy/reboot-device', methods=['POST'])
def proxy_reboot_device():
    try:
        data = request.get_json()
        plc_ip = data.get('ip')
        if not plc_ip:
            return jsonify({'success': False, 'message': 'IP地址不能为空'})

        # 构建PLC设备的重启URL
        plc_url = f"http://{plc_ip}:8090/cgi-bin/reboot?key=reboot"
        
        logger.info(f"Proxying reboot request to: {plc_url}")
        
        # 使用HTTPBasicAuth进行认证
        response = requests.get(
            plc_url, 
            auth=HTTPBasicAuth(PLC_USERNAME, PLC_PASSWORD),
            timeout=5
        )
        
        logger.info(f"Reboot response status code: {response.status_code}")
        
        if response.status_code == 200:
            return jsonify({
                'success': True,
                'message': '设备重启指令已发送'
            })
        else:
            return jsonify({
                'success': False,
                'message': f'设备返回错误状态码: {response.status_code}'
            })

    except requests.exceptions.Timeout:
        logger.error("Reboot request timeout")
        return jsonify({
            'success': False,
            'message': '设备连接超时'
        })
    except Exception as e:
        logger.error(f"Error in proxy_reboot_device: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'代理服务器错误: {str(e)}'
        })

if __name__ == '__main__':
    logger.info(f"Local proxy server starting on port {PROXY_PORT}")
    app.run(host='127.0.0.1', port=PROXY_PORT, debug=True)